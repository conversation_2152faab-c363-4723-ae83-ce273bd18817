import { useState } from "react";
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>2, <PERSON><PERSON>ircle, XCircle } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "./ui/card";
import { Button } from "./ui/button";
import { trpc } from "../utils/trpc";
import { toast } from "sonner";

interface BatchResult {
  success: Array<{
    projectId: string;
    projectTitle: string;
    overallScore: number;
    confidence: number;
  }>;
  errors: Array<{
    projectId: string;
    projectTitle: string;
    error: string;
  }>;
  totalProcessed: number;
}

export function AIAdminPanel() {
  const [isBatchAnalyzing, setIsBatchAnalyzing] = useState(false);
  const [batchResults, setBatchResults] = useState<BatchResult | null>(null);

  const analysesQuery = trpc.aiAnalysis.getAllAnalyses.useQuery();
  
  const batchAnalyzeMutation = trpc.aiAnalysis.batchAnalyzeProjects.useMutation({
    onSuccess: (results) => {
      setBatchResults(results);
      setIsBatchAnalyzing(false);
      toast.success(`Batch analysis completed! ${results.success.length} successful, ${results.errors.length} errors`);
      analysesQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Batch analysis failed: ${error.message}`);
      setIsBatchAnalyzing(false);
    },
  });

  const handleBatchAnalyze = () => {
    setIsBatchAnalyzing(true);
    setBatchResults(null);
    batchAnalyzeMutation.mutate({ analyzeAll: true });
  };

  return (
    <div className="space-y-6">
      {/* Admin Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5" />
            AI Analysis Admin Panel
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-4">
            <Button 
              onClick={handleBatchAnalyze}
              disabled={isBatchAnalyzing}
              className="gap-2"
            >
              {isBatchAnalyzing ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Analyzing All Projects...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4" />
                  Batch Analyze All Projects
                </>
              )}
            </Button>
            
            <Button 
              variant="outline" 
              onClick={() => analysesQuery.refetch()}
              className="gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Refresh
            </Button>
          </div>

          <p className="text-sm text-muted-foreground">
            This will analyze all projects that don't have AI analysis yet. 
            Each analysis takes ~5-10 seconds with rate limiting.
          </p>
        </CardContent>
      </Card>

      {/* Batch Results */}
      {batchResults && (
        <Card>
          <CardHeader>
            <CardTitle>Batch Analysis Results</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="text-2xl font-bold text-green-600">{batchResults.success.length}</div>
                <div className="text-sm text-green-600">Successful</div>
              </div>
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="text-2xl font-bold text-red-600">{batchResults.errors.length}</div>
                <div className="text-sm text-red-600">Errors</div>
              </div>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="text-2xl font-bold text-blue-600">{batchResults.totalProcessed}</div>
                <div className="text-sm text-blue-600">Total Processed</div>
              </div>
            </div>

            {/* Success Results */}
            {batchResults.success.length > 0 && (
              <div>
                <h4 className="font-medium text-green-600 mb-2 flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  Successful Analyses
                </h4>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {batchResults.success.map((result) => (
                    <div key={result.projectId} className="flex justify-between items-center text-sm bg-green-50 p-2 rounded">
                      <span>{result.projectTitle}</span>
                      <div className="flex gap-2">
                        <span className="text-green-600 font-medium">{result.overallScore}%</span>
                        <span className="text-muted-foreground">({result.confidence}% confidence)</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Error Results */}
            {batchResults.errors.length > 0 && (
              <div>
                <h4 className="font-medium text-red-600 mb-2 flex items-center gap-2">
                  <XCircle className="h-4 w-4" />
                  Failed Analyses
                </h4>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {batchResults.errors.map((error) => (
                    <div key={error.projectId} className="text-sm bg-red-50 p-2 rounded">
                      <div className="font-medium">{error.projectTitle}</div>
                      <div className="text-red-600 text-xs">{error.error}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Existing Analyses */}
      <Card>
        <CardHeader>
          <CardTitle>Existing AI Analyses</CardTitle>
        </CardHeader>
        <CardContent>
          {analysesQuery.isLoading && (
            <div className="flex justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
            </div>
          )}

          {analysesQuery.data && analysesQuery.data.length > 0 ? (
            <div className="space-y-3">
              {analysesQuery.data.map((analysis) => (
                <div key={analysis.id} className="flex justify-between items-center p-3 border rounded-lg">
                  <div>
                    <div className="font-medium">{analysis.project.title}</div>
                    <div className="text-sm text-muted-foreground">
                      Updated: {new Date(analysis.updatedAt).toLocaleDateString()}
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <div className="font-bold text-lg">{analysis.overallScore}%</div>
                      <div className="text-xs text-muted-foreground">{analysis.confidence}% confidence</div>
                    </div>
                    <div className="flex gap-1">
                      <div className="w-2 h-8 bg-blue-200 rounded-sm">
                        <div 
                          className="bg-blue-500 rounded-sm transition-all"
                          style={{ height: `${analysis.projectFundamentalsScore}%` }}
                        />
                      </div>
                      <div className="w-2 h-8 bg-green-200 rounded-sm">
                        <div 
                          className="bg-green-500 rounded-sm transition-all"
                          style={{ height: `${analysis.teamGovernanceScore}%` }}
                        />
                      </div>
                      <div className="w-2 h-8 bg-yellow-200 rounded-sm">
                        <div 
                          className="bg-yellow-500 rounded-sm transition-all"
                          style={{ height: `${analysis.transparencyDocScore}%` }}
                        />
                      </div>
                      <div className="w-2 h-8 bg-purple-200 rounded-sm">
                        <div 
                          className="bg-purple-500 rounded-sm transition-all"
                          style={{ height: `${analysis.technologyExecutionScore}%` }}
                        />
                      </div>
                      <div className="w-2 h-8 bg-pink-200 rounded-sm">
                        <div 
                          className="bg-pink-500 rounded-sm transition-all"
                          style={{ height: `${analysis.communityCommunicationScore}%` }}
                        />
                      </div>
                      <div className="w-2 h-8 bg-indigo-200 rounded-sm">
                        <div 
                          className="bg-indigo-500 rounded-sm transition-all"
                          style={{ height: `${analysis.tokenUtilityTokenomicsScore}%` }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              No AI analyses found. Use the batch analyze button to get started.
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
