import { ThumbsUp, ThumbsDown } from "lucide-react";
import { But<PERSON> } from "./ui/button";

interface ThumbsVoteProps {
  value?: boolean;
  onChange: (value: boolean) => void;
  disabled?: boolean;
}

export function ThumbsVote({ value, onChange, disabled = false }: ThumbsVoteProps) {
  return (
    <div className="flex gap-2">
      <Button
        type="button"
        variant={value === true ? "default" : "outline"}
        size="sm"
        onClick={() => onChange(true)}
        disabled={disabled}
        className="h-8 w-8 p-0"
      >
        <ThumbsUp className="h-4 w-4" />
      </Button>
      <Button
        type="button"
        variant={value === false ? "default" : "outline"}
        size="sm"
        onClick={() => onChange(false)}
        disabled={disabled}
        className="h-8 w-8 p-0"
      >
        <ThumbsDown className="h-4 w-4" />
      </Button>
    </div>
  );
}