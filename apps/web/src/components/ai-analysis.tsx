import { useState } from "react";
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>U<PERSON>, <PERSON><PERSON><PERSON>Down, Lightbulb, RefreshCw, Loader2 } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>T<PERSON><PERSON> } from "./ui/card";
import { <PERSON><PERSON> } from "./ui/button";
import { Badge } from "./ui/badge";
import { Spider<PERSON><PERSON> } from "./spider-chart";
import { trpc } from "../utils/trpc";
import { toast } from "sonner";

interface AIAnalysisData {
  id: string;
  projectId: string;
  projectFundamentalsScore: number;
  teamGovernanceScore: number;
  transparencyDocScore: number;
  technologyExecutionScore: number;
  communityCommunicationScore: number;
  tokenUtilityTokenomicsScore: number;
  overallScore: number;
  analysis: string | null;
  reasoning: string | null;
  strengths: string[];
  weaknesses: string[];
  recommendations: string[];
  confidence: number;
  createdAt: string;
  updatedAt: string;
}

interface AIAnalysisProps {
  projectId: string;
  aiAnalysis?: AIAnalysisData | null;
  aiDimensionScores?: Array<{
    dimension: string;
    title: string;
    score: number;
  }> | null;
}

export function AIAnalysis({ projectId, aiAnalysis, aiDimensionScores }: AIAnalysisProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  
  const analyzeProjectMutation = trpc.aiAnalysis.analyzeProject.useMutation({
    onSuccess: () => {
      toast.success("AI analysis completed successfully!");
      setIsAnalyzing(false);
      // Refresh the page data
      window.location.reload();
    },
    onError: (error) => {
      toast.error(`AI analysis failed: ${error.message}`);
      setIsAnalyzing(false);
    },
  });

  const handleAnalyze = async () => {
    setIsAnalyzing(true);
    analyzeProjectMutation.mutate({ projectId });
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return "bg-green-500";
    if (confidence >= 60) return "bg-yellow-500";
    return "bg-red-500";
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  if (!aiAnalysis) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5" />
            AI Analysis
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center py-8">
          <Brain className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <p className="text-muted-foreground mb-4">
            No AI analysis available for this project yet.
          </p>
          <Button 
            onClick={handleAnalyze} 
            disabled={isAnalyzing}
            className="gap-2"
          >
            {isAnalyzing ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Analyzing...
              </>
            ) : (
              <>
                <Brain className="h-4 w-4" />
                Generate AI Analysis
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* AI Spider Chart */}
      {aiDimensionScores && (
        <SpiderChart 
          scores={aiDimensionScores} 
          title="AI Analysis Scores"
          className="border-blue-200 bg-blue-50/50"
        />
      )}

      {/* Overall Analysis */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Bot className="h-5 w-5" />
              AI Analysis Overview
            </CardTitle>
            <div className="flex items-center gap-2">
              <Badge 
                variant="secondary" 
                className={`${getConfidenceColor(aiAnalysis.confidence)} text-white`}
              >
                {aiAnalysis.confidence}% Confidence
              </Badge>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleAnalyze}
                disabled={isAnalyzing}
                className="gap-1"
              >
                {isAnalyzing ? (
                  <Loader2 className="h-3 w-3 animate-spin" />
                ) : (
                  <RefreshCw className="h-3 w-3" />
                )}
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Overall Score */}
          <div className="text-center p-4 bg-muted rounded-lg">
            <div className={`text-3xl font-bold ${getScoreColor(aiAnalysis.overallScore)}`}>
              {aiAnalysis.overallScore}/100
            </div>
            <p className="text-sm text-muted-foreground">Overall AI Score</p>
          </div>

          {/* Analysis Text */}
          {aiAnalysis.analysis && (
            <div>
              <h4 className="font-semibold mb-2">Analysis Summary</h4>
              <p className="text-sm leading-relaxed text-muted-foreground">
                {aiAnalysis.analysis}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Strengths, Weaknesses, Recommendations */}
      <div className="grid md:grid-cols-3 gap-4">
        {/* Strengths */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-green-600">
              <TrendingUp className="h-4 w-4" />
              Strengths
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {aiAnalysis.strengths.map((strength, index) => (
                <li key={index} className="text-sm flex items-start gap-2">
                  <span className="text-green-500 mt-1">•</span>
                  <span>{strength}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        {/* Weaknesses */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-red-600">
              <TrendingDown className="h-4 w-4" />
              Weaknesses
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {aiAnalysis.weaknesses.map((weakness, index) => (
                <li key={index} className="text-sm flex items-start gap-2">
                  <span className="text-red-500 mt-1">•</span>
                  <span>{weakness}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        {/* Recommendations */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-blue-600">
              <Lightbulb className="h-4 w-4" />
              Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {aiAnalysis.recommendations.map((recommendation, index) => (
                <li key={index} className="text-sm flex items-start gap-2">
                  <span className="text-blue-500 mt-1">•</span>
                  <span>{recommendation}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Reasoning */}
      {aiAnalysis.reasoning && (
        <Card>
          <CardHeader>
            <CardTitle>Detailed Reasoning</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm leading-relaxed text-muted-foreground whitespace-pre-wrap">
              {aiAnalysis.reasoning}
            </p>
          </CardContent>
        </Card>
      )}

      {/* Analysis Metadata */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex justify-between items-center text-xs text-muted-foreground">
            <span>
              Analysis created: {new Date(aiAnalysis.createdAt).toLocaleDateString()}
            </span>
            <span>
              Last updated: {new Date(aiAnalysis.updatedAt).toLocaleDateString()}
            </span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
