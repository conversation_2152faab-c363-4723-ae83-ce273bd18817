import { <PERSON><PERSON>hart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, ResponsiveContainer } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from "./ui/card";

interface DimensionScore {
  dimension: string;
  title: string;
  score: number;
}

interface SpiderChartProps {
  scores: DimensionScore[];
  title?: string;
  className?: string;
}

export function SpiderChart({ scores, title = "Review Scores", className }: SpiderChartProps) {
  const data = scores.map(score => ({
    subject: score.title.split(' ').slice(0, 2).join(' '), // Shorten labels
    fullTitle: score.title,
    score: score.score,
  }));

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-center">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-80 w-full">
          <ResponsiveContainer width="100%" height="100%">
            <RadarChart data={data}>
              <PolarGrid />
              <PolarAngleAxis 
                dataKey="subject" 
                tick={{ fontSize: 12 }}
                className="text-xs"
              />
              <PolarRadiusAxis 
                angle={90} 
                domain={[0, 100]} 
                tick={{ fontSize: 10 }}
                tickCount={6}
              />
              <Radar
                name="Score"
                dataKey="score"
                stroke="#8884d8"
                fill="#8884d8"
                fillOpacity={0.3}
                strokeWidth={2}
              />
            </RadarChart>
          </ResponsiveContainer>
        </div>
        
        {/* Legend with full titles */}
        <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 gap-2 text-xs">
          {data.map((item, index) => (
            <div key={index} className="flex justify-between items-center">
              <span className="text-muted-foreground">{item.fullTitle}:</span>
              <span className="font-medium">{item.score}%</span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}