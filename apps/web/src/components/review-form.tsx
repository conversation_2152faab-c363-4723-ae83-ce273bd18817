import { useState } from "react";
import { useNavigate } from "react-router";
import { But<PERSON> } from "./ui/button";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "./ui/card";
import { Label } from "./ui/label";
import { Textarea } from "./ui/textarea";
import { DimensionReview } from "./dimension-review";
import { ThumbsVote } from "./thumbs-vote";
import { toast } from "sonner";
import { REVIEW_DIMENSIONS, type ReviewDimension } from "../lib/review-constants";
import { trpc } from "../utils/trpc";

interface ReviewFormData {
  [key: string]: Array<{ vote?: boolean; feedback?: string }>;
}

interface DimensionFeedback {
  [key: string]: string;
}

interface Project {
  id: string;
  title: string;
  description: string;
  imageUrl: string | null;
  websiteUrl: string | null;
  deckUrl: string | null;
  whitepaperUrl: string | null;
  socialUrls: any;
  challengeIntro: string;
}

interface ReviewFormProps {
  project: Project;
}

export function ReviewForm({ project }: ReviewFormProps) {
  const navigate = useNavigate();
  const [overallSentiment, setOverallSentiment] = useState<boolean | undefined>();
  const [overallComments, setOverallComments] = useState("");
  const [dimensionResponses, setDimensionResponses] = useState<ReviewFormData>(() => {
    const initial: ReviewFormData = {};
    REVIEW_DIMENSIONS.forEach(dimension => {
      initial[dimension.key] = dimension.questions.map(() => ({}));
    });
    return initial;
  });

  const [dimensionFeedback, setDimensionFeedback] = useState<DimensionFeedback>(() => {
    const initial: DimensionFeedback = {};
    REVIEW_DIMENSIONS.forEach(dimension => {
      initial[dimension.key] = "";
    });
    return initial;
  });

  const createReviewMutation = trpc.reviews.create.useMutation({
    onSuccess: () => {
      toast.success("Review submitted successfully! AI validation has been applied to your feedback.");
      navigate(`/projects/${project.id}`);
    },
    onError: (error) => {
      toast.error(`Failed to submit review: ${error.message}`);
    },
  });

  const handleDimensionResponseChange = (
    dimensionKey: ReviewDimension,
    questionIndex: number,
    response: { vote?: boolean; feedback?: string }
  ) => {
    setDimensionResponses(prev => ({
      ...prev,
      [dimensionKey]: prev[dimensionKey].map((existing, idx) =>
        idx === questionIndex ? { ...existing, ...response } : existing
      ),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (overallSentiment === undefined) {
      alert("Please provide an overall sentiment");
      return;
    }

    // Flatten responses for submission
    const responses = REVIEW_DIMENSIONS.flatMap(dimension =>
      dimension.questions.map((_, questionIndex) => {
        const response = dimensionResponses[dimension.key][questionIndex];
        return {
          dimension: dimension.key,
          questionIndex,
          vote: response.vote ?? false,
          feedback: dimensionFeedback[dimension.key] || "",
        };
      })
    );

    // Check if all questions have votes
    const votesGiven = responses.filter(r => r.vote !== undefined);
    if (votesGiven.length === 0) {
      alert("Please answer at least some questions before submitting");
      return;
    }

    createReviewMutation.mutate({
      projectId: project.id,
      overallSentiment,
      overallComments,
      responses,
    });
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Project Header */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="w-full sm:w-32 h-32 sm:h-20 bg-muted rounded-lg flex items-center justify-center overflow-hidden">
              {project.imageUrl ? (
                <img 
                  src={project.imageUrl} 
                  alt={project.title}
                  className="w-full h-full object-cover"
                />
              ) : (
                <span className="text-2xl">IMG</span>
              )}
            </div>
            <div className="flex-1">
              <CardTitle className="text-2xl mb-2">{project.title}</CardTitle>
              <p className="text-muted-foreground">{project.description}</p>
              
              {/* Project Links */}
              <div className="flex flex-wrap gap-2 mt-3">
                {project.websiteUrl && (
                  <Button variant="outline" size="sm" asChild>
                    <a href={project.websiteUrl} target="_blank" rel="noopener noreferrer">
                      Website
                    </a>
                  </Button>
                )}
                {project.deckUrl && (
                  <Button variant="outline" size="sm" asChild>
                    <a href={project.deckUrl} target="_blank" rel="noopener noreferrer">
                      Deck
                    </a>
                  </Button>
                )}
                {project.whitepaperUrl && (
                  <Button variant="outline" size="sm" asChild>
                    <a href={project.whitepaperUrl} target="_blank" rel="noopener noreferrer">
                      Whitepaper
                    </a>
                  </Button>
                )}
                {project.socialUrls && (
                  <Button variant="outline" size="sm" asChild>
                    <a href="#" target="_blank" rel="noopener noreferrer">
                      Socials
                    </a>
                  </Button>
                )}
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-sm leading-relaxed">{project.challengeIntro}</p>
          </div>
        </CardContent>
      </Card>

      {/* Review Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Dimension Reviews */}
        {REVIEW_DIMENSIONS.map((dimension, index) => (
          <DimensionReview
            key={dimension.key}
            dimension={dimension}
            responses={dimensionResponses[dimension.key]}
            onResponseChange={(questionIndex, response) =>
              handleDimensionResponseChange(dimension.key, questionIndex, response)
            }
            feedback={dimensionFeedback[dimension.key]}
            onFeedbackChange={(feedback) => 
              setDimensionFeedback(prev => ({ ...prev, [dimension.key]: feedback }))
            }
            disabled={createReviewMutation.isPending}
          />
        ))}

        {/* Overall Sentiment */}
        <Card>
          <CardHeader>
            <CardTitle>Overall Sentiment</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-base">Want to see this listed paragraph?</Label>
              <ThumbsVote
                value={overallSentiment}
                onChange={setOverallSentiment}
                disabled={createReviewMutation.isPending}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="overall-feedback">Overall Feedback</Label>
              <Textarea
                id="overall-feedback"
                placeholder="Share your overall thoughts about this project..."
                value={overallComments}
                onChange={(e) => setOverallComments(e.target.value)}
                disabled={createReviewMutation.isPending}
                className="min-h-[100px]"
              />
            </div>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="text-center space-y-3">
          <Button 
            type="submit" 
            size="lg" 
            disabled={createReviewMutation.isPending}
            className="w-full sm:w-auto"
          >
            {createReviewMutation.isPending ? "Submitting & Validating..." : "Submit Review"}
          </Button>
          <p className="text-xs text-muted-foreground">
            🤖 AI will validate your feedback for relevance and quality
          </p>
        </div>
      </form>

      {/* CTA Box */}
      <Card className="bg-muted/50">
        <CardContent className="p-6 text-center">
          <p className="text-sm text-muted-foreground">CTA box TBD</p>
        </CardContent>
      </Card>
    </div>
  );
}