import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "./ui/card";
import { Label } from "./ui/label";
import { Textarea } from "./ui/textarea";
import { ThumbsVote } from "./thumbs-vote";
import type { DimensionInfo } from "../lib/review-constants";

interface QuestionResponse {
  vote?: boolean;
  feedback?: string;
}

interface DimensionReviewProps {
  dimension: DimensionInfo;
  responses: QuestionResponse[];
  onResponseChange: (questionIndex: number, response: QuestionResponse) => void;
  feedback: string;
  onFeedbackChange: (feedback: string) => void;
  disabled?: boolean;
}

export function DimensionReview({ 
  dimension, 
  responses, 
  onResponseChange,
  feedback,
  onFeedbackChange,
  disabled = false 
}: DimensionReviewProps) {
  const handleVoteChange = (questionIndex: number, vote: boolean) => {
    onResponseChange(questionIndex, {
      ...responses[questionIndex],
      vote,
    });
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-lg font-semibold">
          {dimension.title}
        </CardTitle>
        {dimension.subtitle && (
          <p className="text-sm text-muted-foreground">{dimension.subtitle}</p>
        )}
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Questions Section */}
          <div className="space-y-4">
            <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
              Questions
            </h4>
            <div className="space-y-4">
              {dimension.questions.map((question, index) => (
                <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                  <div className="flex-1">
                    <Label className="text-sm leading-relaxed">{question}</Label>
                  </div>
                  <ThumbsVote
                    value={responses[index]?.vote}
                    onChange={(vote) => handleVoteChange(index, vote)}
                    disabled={disabled}
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Feedback Section */}
          <div className="space-y-4">
            <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
              Written Feedback
            </h4>
            <Textarea
              placeholder="Share your detailed thoughts on this dimension..."
              value={feedback}
              onChange={(e) => onFeedbackChange(e.target.value)}
              disabled={disabled}
              className="min-h-[200px] resize-none"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}