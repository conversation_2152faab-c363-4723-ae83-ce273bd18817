import { ExternalLink, FileText, Globe, Users } from "lucide-react";
import { Link } from "react-router";
import { Button } from "./ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "./ui/card";

interface Project {
  id: string;
  title: string;
  description: string;
  imageUrl: string | null;
  websiteUrl: string | null;
  deckUrl: string | null;
  whitepaperUrl: string | null;
  socialUrls: any;
  challengeIntro: string;
  isApprovedForVoting: boolean;
  createdAt: string;
}

interface ProjectCardProps {
  project: Project;
}

export function ProjectCard({ project }: ProjectCardProps) {
  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Project Image */}
          <div className="w-full sm:w-32 h-32 sm:h-20 bg-muted rounded-lg flex items-center justify-center overflow-hidden">
            {project.imageUrl ? (
              <img 
                src={project.imageUrl} 
                alt={project.title}
                className="w-full h-full object-cover"
              />
            ) : (
              <span className="text-2xl">IMG</span>
            )}
          </div>

          {/* Project Info */}
          <div className="flex-1 space-y-2">
            <CardTitle className="text-xl">{project.title}</CardTitle>
            <p className="text-sm text-muted-foreground line-clamp-2">
              {project.description}
            </p>
            
            {/* Links */}
            <div className="flex flex-wrap gap-2">
              {project.websiteUrl && (
                <Button variant="outline" size="sm" asChild>
                  <a href={project.websiteUrl} target="_blank" rel="noopener noreferrer">
                    <Globe className="h-3 w-3 mr-1" />
                    Website
                  </a>
                </Button>
              )}
              {project.deckUrl && (
                <Button variant="outline" size="sm" asChild>
                  <a href={project.deckUrl} target="_blank" rel="noopener noreferrer">
                    <FileText className="h-3 w-3 mr-1" />
                    Deck
                  </a>
                </Button>
              )}
              {project.whitepaperUrl && (
                <Button variant="outline" size="sm" asChild>
                  <a href={project.whitepaperUrl} target="_blank" rel="noopener noreferrer">
                    <FileText className="h-3 w-3 mr-1" />
                    Whitepaper
                  </a>
                </Button>
              )}
              {project.socialUrls && (
                <Button variant="outline" size="sm" asChild>
                  <a href="#" target="_blank" rel="noopener noreferrer">
                    <Users className="h-3 w-3 mr-1" />
                    Socials
                  </a>
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* KYC Notice */}
        <div className="bg-pink-50 border border-pink-200 rounded-lg p-3 text-right">
          <span className="text-xs text-pink-700 font-medium">
            This is for KYC stakers only
          </span>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Challenge Intro */}
        <div className="space-y-3">
          <p className="text-sm leading-relaxed text-muted-foreground">
            {project.challengeIntro}
          </p>

          {/* Actions */}
          <div className="flex justify-between items-center">
            <Button variant="outline" asChild>
              <Link to={`/projects/${project.id}`}>
                <ExternalLink className="h-4 w-4 mr-2" />
                View Details
              </Link>
            </Button>
            
            {project.isApprovedForVoting && (
              <Button asChild>
                <Link to={`/submit-review?projectId=${project.id}`}>
                  Submit Review
                </Link>
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}