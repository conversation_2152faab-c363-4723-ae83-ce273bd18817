export enum ReviewDimension {
  PROJECT_FUNDAMENTALS = "PROJECT_FUNDAMENTALS",
  TEAM_GOVERNANCE = "TEAM_GOVERNANCE", 
  TRANSPARENCY_DOCUMENTATION = "TRANSPARENCY_DOCUMENTATION",
  TECHNOLOGY_EXECUTION = "TECHNOLOGY_EXECUTION",
  COMMUNITY_COMMUNICATION = "COMMUNITY_COMMUNICATION",
  TOKEN_UTILITY_TOKENOMICS = "TOKEN_UTILITY_TOKENOMICS"
}

export interface DimensionInfo {
  key: ReviewDimension;
  title: string;
  subtitle?: string;
  questions: string[];
}

export const REVIEW_DIMENSIONS: DimensionInfo[] = [
  {
    key: ReviewDimension.PROJECT_FUNDAMENTALS,
    title: "Project Fundamentals",
    subtitle: "Integrated: Narrative Strength + GTM Strategy",
    questions: [
      "How clearly is the product vision articulated?",
      "How original or differentiated is the idea compared to existing projects?",
      "Is there a real market demand or defined pain point the product addresses?",
      "Does the project present a compelling narrative or cultural hook?",
      "Can the concept be easily communicated in 1–2 sentences (\"memeable\")?",
      "How likely is the idea to gain viral traction or benefit from social momentum?",
      "Does the project have a coherent go-to-market strategy?",
      "Are the growth channels (e.g., influencers, integrations, community-led growth) well-defined?"
    ]
  },
  {
    key: ReviewDimension.TEAM_GOVERNANCE,
    title: "Team & Governance", 
    questions: [
      "Do the founders have relevant and verifiable experience?",
      "Is the identity of the core team publicly verifiable?",
      "Are all key functional areas (tech, product, marketing, legal, operations) adequately covered?",
      "Does the governance structure reflect transparency and accountability? (if this is a feature)",
      "Are advisors credible and actively involved?",
      "Is there a roadmap for progressive decentralization?"
    ]
  },
  {
    key: ReviewDimension.TRANSPARENCY_DOCUMENTATION,
    title: "Transparency & Documentation",
    questions: [
      "Is the website professional, complete, and informative?",
      "Are whitepaper and/or Gitbook detailed and technically sound?",
      "Are risk disclosures, disclaimers, and legal structures clearly explained?",
      "Is there sufficient explanation of business model, and protocol operations?",
      "Does the roadmap provide a realistic timeline and measurable milestones?",
      "Are investor terms and compliance processes (KYC, SAFT, etc.) disclosed where relevant?"
    ]
  },
  {
    key: ReviewDimension.TECHNOLOGY_EXECUTION,
    title: "Technology & Execution",
    questions: [
      "Is there an MVP, prototype, or demo available?",
      "Is the codebase public? Is there regular and recent GitHub activity?",
      "Are smart contracts audited or undergoing security reviews?",
      "Is the tech stack appropriate and scalable?",
      "Does the system architecture allow for modular growth or integrations?",
      "Are there dev team bios, repos, or activity logs to assess real progress?"
    ]
  },
  {
    key: ReviewDimension.COMMUNITY_COMMUNICATION,
    title: "Community & Communication",
    questions: [
      "Is the project growing its community organically across key platforms (X, Discord, Telegram)?",
      "Is there consistent communication (AMAs, updates, roadmap progress)?",
      "Is the tone of engagement professional, transparent, and interactive?",
      "Are there clear community feedback loops or governance mechanisms (polls, discussions)?",
      "Is the branding cohesive and memorable?",
      "Is the project leveraging media or narratives to build trust and hype effectively?"
    ]
  },
  {
    key: ReviewDimension.TOKEN_UTILITY_TOKENOMICS,
    title: "Token Utility & Tokenomics",
    questions: [
      "Is the token integral to the protocol (beyond speculation)?",
      "Is the allocation structure fair across stakeholders (founders, investors, community)?",
      "Are tokenomics aligned with long-term sustainability (burns, buybacks, inflation caps)?",
      "Are vesting schedules reasonable and aligned with value creation?",
      "Are incentives structured to retain users and contributors?",
      "Is the token designed to accrue value through utility, participation, or ecosystem growth?"
    ]
  }
];

export const getDimensionInfo = (dimension: ReviewDimension): DimensionInfo => {
  const info = REVIEW_DIMENSIONS.find(d => d.key === dimension);
  if (!info) {
    throw new Error(`Unknown dimension: ${dimension}`);
  }
  return info;
};

export const calculateDimensionScore = (responses: { vote: boolean }[]): number => {
  if (responses.length === 0) return 0;
  const positiveVotes = responses.filter(r => r.vote).length;
  return Math.round((positiveVotes / responses.length) * 100);
};