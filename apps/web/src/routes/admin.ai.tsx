import type { Route } from "./+types/admin.ai";
import { AIAdminPanel } from "@/components/ai-admin-panel";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "AI Analysis Admin - ATTN CIE" },
    { name: "description", content: "Admin panel for managing AI project analyses" }
  ];
}

export default function AIAdmin() {
  return (
    <div className="container mx-auto max-w-6xl px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">AI Analysis Administration</h1>
        <p className="text-muted-foreground">
          Manage and monitor AI-powered project analyses across all projects.
        </p>
      </div>

      <AIAdminPanel />
    </div>
  );
}
