import type { Route } from "./+types/_index";
import { trpc } from "@/utils/trpc";
import { ProjectCard } from "@/components/project-card";
import { Loader } from "@/components/loader";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "ATTN - Community Intelligence Engine" }, 
    { name: "description", content: "Community review system for blockchain projects" }
  ];
}

export default function Home() {
  const projectsQuery = trpc.projects.list.useQuery();
  
  return (
    <div className="container mx-auto max-w-6xl px-4 py-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-2">Community Intelligence Engine</h1>
        <p className="text-xl text-muted-foreground">
          Review blockchain projects across 6 key dimensions
        </p>
      </div>

      {/* Projects List */}
      <div className="space-y-6">
        <h2 className="text-2xl font-semibold">Projects Available for Review</h2>
        
        {projectsQuery.isLoading && (
          <div className="flex justify-center py-8">
            <Loader />
          </div>
        )}

        {projectsQuery.error && (
          <div className="text-center py-8 text-red-600">
            Error loading projects: {projectsQuery.error.message}
          </div>
        )}

        {projectsQuery.data && (
          <div className="grid gap-6">
            {(projectsQuery.data as any[]).map((project: any) => (
              <ProjectCard key={project.id} project={project} />
            ))}
            
            {projectsQuery.data.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                No projects available for review yet.
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
