import type { Route } from "./+types/projects.$id.review";
import { use<PERSON>ara<PERSON>, <PERSON> } from "react-router";
import { ArrowLeft } from "lucide-react";
import { trpc } from "@/utils/trpc";
import { Button } from "@/components/ui/button";
import { ReviewForm } from "@/components/review-form";
import { Loader } from "@/components/loader";

export function meta({ params }: Route.MetaArgs) {
  return [
    { title: `Submit Review - ATTN CIE` },
    { name: "description", content: `Submit your review for this project` }
  ];
}

export default function ProjectReview() {
  const { id } = useParams();
  const projectQuery = trpc.projects.getById.useQuery({ id: id! });
  const existingReviewQuery = trpc.reviews.getUserReviewForProject.useQuery({ projectId: id! });

  if (projectQuery.isLoading || existingReviewQuery.isLoading) {
    return (
      <div className="container mx-auto max-w-6xl px-4 py-8">
        <div className="flex justify-center py-16">
          <Loader />
        </div>
      </div>
    );
  }

  if (projectQuery.error || !projectQuery.data) {
    return (
      <div className="container mx-auto max-w-6xl px-4 py-8">
        <div className="text-center py-16">
          <h1 className="text-2xl font-bold mb-4">Project Not Found</h1>
          <p className="text-muted-foreground mb-6">
            The project you're looking for doesn't exist or has been removed.
          </p>
          <Button asChild>
            <Link to="/">Return Home</Link>
          </Button>
        </div>
      </div>
    );
  }

  const project = projectQuery.data;

  // Check if project is approved for voting
  if (!project.isApprovedForVoting) {
    return (
      <div className="container mx-auto max-w-6xl px-4 py-8">
        <Button variant="ghost" asChild className="mb-6">
          <Link to={`/projects/${id}`}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Project
          </Link>
        </Button>
        
        <div className="text-center py-16">
          <h1 className="text-2xl font-bold mb-4">Review Not Available</h1>
          <p className="text-muted-foreground mb-6">
            This project is not currently approved for community voting.
          </p>
          <Button asChild>
            <Link to={`/projects/${id}`}>View Project Details</Link>
          </Button>
        </div>
      </div>
    );
  }

  // Check if user already reviewed this project
  if (existingReviewQuery.data) {
    return (
      <div className="container mx-auto max-w-6xl px-4 py-8">
        <Button variant="ghost" asChild className="mb-6">
          <Link to={`/projects/${id}`}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Project
          </Link>
        </Button>
        
        <div className="text-center py-16">
          <h1 className="text-2xl font-bold mb-4">Review Already Submitted</h1>
          <p className="text-muted-foreground mb-6">
            You have already submitted a review for this project. Each user can only submit one review per project.
          </p>
          <div className="flex gap-4 justify-center">
            <Button asChild>
              <Link to={`/projects/${id}`}>View Project & Reviews</Link>
            </Button>
            <Button variant="outline" asChild>
              <Link to="/">Browse Other Projects</Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-6xl px-4 py-8">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <Button variant="ghost" asChild>
          <Link to={`/projects/${id}`}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Project
          </Link>
        </Button>
        
        <div className="text-center">
          <h1 className="text-3xl font-bold">Submit Review</h1>
          <p className="text-muted-foreground">
            Share your assessment across 6 key dimensions
          </p>
        </div>
        
        <div /> {/* Spacer for centering */}
      </div>

      {/* Review Form */}
      <ReviewForm project={project as any} />
    </div>
  );
}