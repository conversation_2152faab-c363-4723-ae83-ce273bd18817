{"name": "server", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "db:push": "prisma db push --schema ./prisma/schema", "db:studio": "prisma studio", "db:generate": "prisma generate --schema ./prisma/schema", "db:migrate": "prisma migrate dev", "db:seed": "node --loader tsx/esm src/lib/seed.ts"}, "dependencies": {"@prisma/client": "^6.9.0", "@trpc/client": "^11.0.0", "@trpc/server": "^11.0.0", "dotenv": "^16.5.0", "next": "15.3.0", "openai": "^5.3.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "prisma": "^6.9.0", "tsx": "^4.20.3", "typescript": "^5"}}