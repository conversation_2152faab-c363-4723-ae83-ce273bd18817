import { fetchRe<PERSON><PERSON>and<PERSON> } from '@trpc/server/adapters/fetch';
import { appRouter } from '@/routers';
import { createContext } from '@/lib/context';
import { NextRequest, NextResponse } from 'next/server';

// Handle OPTIONS request for CORS preflight
export async function OPTIONS(req: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-user-id',
      'Access-Control-Allow-Credentials': 'true',
    },
  });
}

async function handler(req: NextRequest) {
  const response = await fetchRequestHandler({
    endpoint: '/trpc',
    req,
    router: appRouter,
    createContext: () => createContext(req),
  });

  // Add CORS headers to the response
  const corsHeaders = {
    'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-user-id',
    'Access-Control-Allow-Credentials': 'true',
  };

  // If response is a Response object, clone it and add headers
  if (response instanceof Response) {
    const newResponse = new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        ...Object.fromEntries(response.headers.entries()),
        ...corsHeaders,
      },
    });
    return newResponse;
  }

  return response;
}

export { handler as GET, handler as POST };
