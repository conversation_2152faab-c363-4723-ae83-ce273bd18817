import "dotenv/config";
import { PrismaClient } from "../../prisma/generated/index.js";

const prisma = new PrismaClient();

async function seed() {
  console.log("🌱 Seeding database...");

  // Create test users
  const users = await Promise.all([
    prisma.user.upsert({
      where: { id: "user_1" },
      update: {},
      create: {
        id: "user_1",
        email: "<EMAIL>",
        name: "<PERSON>",
        isKYCVerified: true,
      },
    }),
    prisma.user.upsert({
      where: { id: "user_2" },
      update: {},
      create: {
        id: "user_2",
        email: "<EMAIL>",
        name: "<PERSON>",
        isKYCVerified: true,
      },
    }),
    prisma.user.upsert({
      where: { id: "user_3" },
      update: {},
      create: {
        id: "user_3",
        email: "<EMAIL>",
        name: "<PERSON>",
        isKYCVerified: false,
      },
    }),
  ]);

  console.log(`✅ Created ${users.length} users`);

  // Create test projects
  const projects = await Promise.all([
    prisma.project.upsert({
      where: { id: "project_1" },
      update: {},
      create: {
        id: "project_1",
        title: "DeFi Protocol X",
        description: "A next-generation decentralized exchange with innovative AMM mechanics and cross-chain capabilities.",
        imageUrl: "https://via.placeholder.com/400x200/4f46e5/ffffff?text=DeFi+Protocol+X",
        websiteUrl: "https://defiprotocolx.com",
        deckUrl: "https://example.com/deck.pdf",
        whitepaperUrl: "https://example.com/whitepaper.pdf",
        socialUrls: [
          "https://twitter.com/defiprotocolx",
          "https://discord.gg/defiprotocolx",
          "https://t.me/defiprotocolx"
        ],
        challengeIntro: "DeFi Protocol X aims to solve the liquidity fragmentation problem across multiple blockchains. Key challenge points: Cross-chain interoperability, MEV protection, sustainable tokenomics. Total potential market: $50B+ DeFi TVL.",
        isApprovedForVoting: true,
      },
    }),
    prisma.project.upsert({
      where: { id: "project_2" },
      update: {},
      create: {
        id: "project_2",
        title: "GameFi Arena",
        description: "Play-to-earn gaming platform with NFT integration and competitive tournaments.",
        imageUrl: "https://via.placeholder.com/400x200/059669/ffffff?text=GameFi+Arena",
        websiteUrl: "https://gamefiarena.io",
        deckUrl: "https://example.com/gamefi-deck.pdf",
        whitepaperUrl: "https://example.com/gamefi-whitepaper.pdf",
        socialUrls: [
          "https://twitter.com/gamefiarena",
          "https://discord.gg/gamefiarena"
        ],
        challengeIntro: "GameFi Arena is building the ultimate competitive gaming ecosystem. Key challenges: Player retention, sustainable reward mechanisms, anti-bot measures. Target market: 3.2B gamers worldwide with $200B+ gaming market.",
        isApprovedForVoting: true,
      },
    }),
    prisma.project.upsert({
      where: { id: "project_3" },
      update: {},
      create: {
        id: "project_3",
        title: "Carbon Credit Chain",
        description: "Blockchain-based carbon credit marketplace with transparent tracking and verification.",
        imageUrl: "https://via.placeholder.com/400x200/16a34a/ffffff?text=Carbon+Credit+Chain",
        websiteUrl: "https://carboncreditchain.org",
        deckUrl: "https://example.com/carbon-deck.pdf",
        whitepaperUrl: "https://example.com/carbon-whitepaper.pdf",
        socialUrls: [
          "https://twitter.com/carboncreditchain",
          "https://linkedin.com/company/carboncreditchain"
        ],
        challengeIntro: "Carbon Credit Chain digitizes and democratizes carbon markets. Key challenges: Verification of real-world carbon offsets, regulatory compliance, market liquidity. Market opportunity: $1T+ carbon credit market by 2030.",
        isApprovedForVoting: false, // This one is not approved for voting yet
      },
    }),
  ]);

  console.log(`✅ Created ${projects.length} projects`);

  console.log("🎉 Seeding completed!");
}

seed()
  .catch((e) => {
    console.error("❌ Seeding failed:");
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });