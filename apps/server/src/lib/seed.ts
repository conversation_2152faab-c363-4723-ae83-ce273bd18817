import "dotenv/config";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function seed() {
  console.log("🌱 Seeding database...");

  // Create test users
  const users = await Promise.all([
    prisma.user.upsert({
      where: { id: "user_1" },
      update: {},
      create: {
        id: "user_1",
        email: "<EMAIL>",
        name: "<PERSON>",
        isKYCVerified: true,
      },
    }),
    prisma.user.upsert({
      where: { id: "user_2" },
      update: {},
      create: {
        id: "user_2",
        email: "<EMAIL>",
        name: "<PERSON>",
        isKYCVerified: true,
      },
    }),
    prisma.user.upsert({
      where: { id: "user_3" },
      update: {},
      create: {
        id: "user_3",
        email: "<EMAIL>",
        name: "<PERSON>",
        isKYCVerified: false,
      },
    }),
  ]);

  console.log(`✅ Created ${users.length} users`);

  // Create test projects
  const projects = await Promise.all([
    prisma.project.upsert({
      where: { id: "project_1" },
      update: {},
      create: {
        id: "project_1",
        title: "DeFi Protocol X",
        description: "A next-generation decentralized exchange with innovative AMM mechanics and cross-chain capabilities.",
        imageUrl: "https://via.placeholder.com/400x200/4f46e5/ffffff?text=DeFi+Protocol+X",
        websiteUrl: "https://defiprotocolx.com",
        deckUrl: "https://example.com/deck.pdf",
        whitepaperUrl: "https://example.com/whitepaper.pdf",
        socialUrls: [
          "https://twitter.com/defiprotocolx",
          "https://discord.gg/defiprotocolx",
          "https://t.me/defiprotocolx"
        ],
        challengeIntro: "DeFi Protocol X aims to solve the liquidity fragmentation problem across multiple blockchains. Key challenge points: Cross-chain interoperability, MEV protection, sustainable tokenomics. Total potential market: $50B+ DeFi TVL.",
        isApprovedForVoting: true,
      },
    }),
    prisma.project.upsert({
      where: { id: "project_2" },
      update: {},
      create: {
        id: "project_2",
        title: "GameFi Arena",
        description: "Play-to-earn gaming platform with NFT integration and competitive tournaments.",
        imageUrl: "https://via.placeholder.com/400x200/059669/ffffff?text=GameFi+Arena",
        websiteUrl: "https://gamefiarena.io",
        deckUrl: "https://example.com/gamefi-deck.pdf",
        whitepaperUrl: "https://example.com/gamefi-whitepaper.pdf",
        socialUrls: [
          "https://twitter.com/gamefiarena",
          "https://discord.gg/gamefiarena"
        ],
        challengeIntro: "GameFi Arena is building the ultimate competitive gaming ecosystem. Key challenges: Player retention, sustainable reward mechanisms, anti-bot measures. Target market: 3.2B gamers worldwide with $200B+ gaming market.",
        isApprovedForVoting: true,
      },
    }),
    prisma.project.upsert({
      where: { id: "project_3" },
      update: {},
      create: {
        id: "project_3",
        title: "Carbon Credit Chain",
        description: "Blockchain-based carbon credit marketplace with transparent tracking and verification.",
        imageUrl: "https://via.placeholder.com/400x200/16a34a/ffffff?text=Carbon+Credit+Chain",
        websiteUrl: "https://carboncreditchain.org",
        deckUrl: "https://example.com/carbon-deck.pdf",
        whitepaperUrl: "https://example.com/carbon-whitepaper.pdf",
        socialUrls: [
          "https://twitter.com/carboncreditchain",
          "https://linkedin.com/company/carboncreditchain"
        ],
        challengeIntro: "Carbon Credit Chain digitizes and democratizes carbon markets. Key challenges: Verification of real-world carbon offsets, regulatory compliance, market liquidity. Market opportunity: $1T+ carbon credit market by 2030.",
        isApprovedForVoting: true,
      },
    }),
    prisma.project.upsert({
      where: { id: "project_4" },
      update: {},
      create: {
        id: "project_4",
        title: "ZK Privacy Layer",
        description: "Zero-knowledge privacy infrastructure for Web3 applications with seamless integration.",
        imageUrl: "https://via.placeholder.com/400x200/7c3aed/ffffff?text=ZK+Privacy+Layer",
        websiteUrl: "https://zkprivacylayer.dev",
        deckUrl: "https://example.com/zk-deck.pdf",
        whitepaperUrl: "https://example.com/zk-whitepaper.pdf",
        socialUrls: [
          "https://twitter.com/zkprivacylayer",
          "https://github.com/zkprivacylayer",
          "https://discord.gg/zkprivacylayer"
        ],
        challengeIntro: "ZK Privacy Layer provides plug-and-play privacy for any dApp. Key challenges: ZK proof generation speed, developer adoption, regulatory compliance. Market: $10B+ privacy tech market growing 25% annually.",
        isApprovedForVoting: true,
      },
    }),
    prisma.project.upsert({
      where: { id: "project_5" },
      update: {},
      create: {
        id: "project_5",
        title: "SupplyChain DAO",
        description: "Decentralized supply chain management with real-time tracking and automated compliance.",
        imageUrl: "https://via.placeholder.com/400x200/dc2626/ffffff?text=SupplyChain+DAO",
        websiteUrl: "https://supplychaindao.org",
        deckUrl: "https://example.com/supply-deck.pdf",
        whitepaperUrl: "https://example.com/supply-whitepaper.pdf",
        socialUrls: [
          "https://twitter.com/supplychaindao",
          "https://linkedin.com/company/supplychaindao",
          "https://t.me/supplychaindao"
        ],
        challengeIntro: "SupplyChain DAO revolutionizes global logistics with blockchain transparency. Key challenges: IoT integration, enterprise adoption, cross-border regulations. Target: $15T global supply chain market.",
        isApprovedForVoting: true,
      },
    }),
    prisma.project.upsert({
      where: { id: "project_6" },
      update: {},
      create: {
        id: "project_6",
        title: "MetaVerse Builder",
        description: "No-code platform for creating immersive metaverse experiences with NFT integration.",
        imageUrl: "https://via.placeholder.com/400x200/f59e0b/ffffff?text=MetaVerse+Builder",
        websiteUrl: "https://metaversebuilder.io",
        deckUrl: "https://example.com/metaverse-deck.pdf",
        whitepaperUrl: "https://example.com/metaverse-whitepaper.pdf",
        socialUrls: [
          "https://twitter.com/metaversebuilder",
          "https://discord.gg/metaversebuilder",
          "https://youtube.com/metaversebuilder"
        ],
        challengeIntro: "MetaVerse Builder democratizes metaverse creation for everyone. Key challenges: User experience simplification, cross-platform compatibility, content monetization. Market: $800B+ metaverse opportunity by 2030.",
        isApprovedForVoting: true,
      },
    }),
    prisma.project.upsert({
      where: { id: "project_7" },
      update: {},
      create: {
        id: "project_7",
        title: "AI Oracle Network",
        description: "Decentralized AI inference network providing real-time machine learning predictions on-chain.",
        imageUrl: "https://via.placeholder.com/400x200/06b6d4/ffffff?text=AI+Oracle+Network",
        websiteUrl: "https://aioraclenetwork.ai",
        deckUrl: "https://example.com/ai-oracle-deck.pdf",
        whitepaperUrl: "https://example.com/ai-oracle-whitepaper.pdf",
        socialUrls: [
          "https://twitter.com/aioraclenetwork",
          "https://github.com/aioraclenetwork",
          "https://discord.gg/aioraclenetwork"
        ],
        challengeIntro: "AI Oracle Network brings AI capabilities directly to smart contracts. Key challenges: Model accuracy verification, computational cost optimization, data privacy. Market: $500B+ AI market intersecting with $3T+ blockchain economy.",
        isApprovedForVoting: true,
      },
    }),
    prisma.project.upsert({
      where: { id: "project_8" },
      update: {},
      create: {
        id: "project_8",
        title: "RealEstate Tokenizer",
        description: "Fractional real estate ownership platform with automated property management and yield distribution.",
        imageUrl: "https://via.placeholder.com/400x200/8b5cf6/ffffff?text=RealEstate+Tokenizer",
        websiteUrl: "https://realestatetokenizer.com",
        deckUrl: "https://example.com/realestate-deck.pdf",
        whitepaperUrl: "https://example.com/realestate-whitepaper.pdf",
        socialUrls: [
          "https://twitter.com/retokenizer",
          "https://linkedin.com/company/realestatetokenizer"
        ],
        challengeIntro: "RealEstate Tokenizer makes property investment accessible to everyone through blockchain. Key challenges: Legal compliance across jurisdictions, property valuation accuracy, liquidity provision. Market: $280T+ global real estate market.",
        isApprovedForVoting: false, // Pending approval
      },
    }),
  ]);

  console.log(`✅ Created ${projects.length} projects`);

  console.log("🎉 Seeding completed!");
}

seed()
  .catch((e) => {
    console.error("❌ Seeding failed:");
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });