import type { NextRequest } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function createContext(req: NextRequest) {
  // Simple user simulation for now - in a real app this would be from auth
  const userId = req.headers.get("x-user-id") || "user_1";

  console.log("🔐 tRPC Context created with userId:", userId);

  return {
    prisma,
    userId,
  };
}

export type Context = Awaited<ReturnType<typeof createContext>>;
