import { z } from "zod";
import { publicProcedure, router } from "../lib/trpc";
import { ReviewDimension, getDimensionInfo } from "../lib/review-constants";
import { validateFeedbackRelevance, validateOverallComments } from "../lib/ai-validation";

const reviewResponseSchema = z.object({
  dimension: z.nativeEnum(ReviewDimension),
  questionIndex: z.number().min(0),
  vote: z.boolean(),
  feedback: z.string().optional(),
});

export const reviewsRouter = router({
  create: publicProcedure
    .input(z.object({
      projectId: z.string(),
      overallSentiment: z.boolean(),
      overallComments: z.string().optional(),
      responses: z.array(reviewResponseSchema),
    }))
    .mutation(async ({ ctx, input }) => {
      console.log("📝 Creating review for userId:", ctx.userId, "projectId:", input.projectId);

      // Check if user already reviewed this project
      const existingReview = await ctx.prisma.review.findUnique({
        where: {
          userId_projectId: {
            userId: ctx.userId,
            projectId: input.projectId,
          },
        },
      });

      if (existingReview) {
        throw new Error("You have already reviewed this project");
      }

      // Get project details for AI validation
      const project = await ctx.prisma.project.findUnique({
        where: { id: input.projectId },
      });

      if (!project) {
        throw new Error("Project not found");
      }

      // Validate overall comments with AI if provided
      let overallValidation = null;
      if (input.overallComments && input.overallComments.trim().length > 0) {
        try {
          overallValidation = await validateOverallComments(
            input.overallComments,
            project.title,
            project.description
          );
        } catch (error) {
          console.error("AI validation failed for overall comments:", error);
        }
      }

      // Validate feedback for each response with AI
      const responsesWithValidation = await Promise.all(
        input.responses.map(async (response) => {
          let validation = null;
          
          if (response.feedback && response.feedback.trim().length > 0) {
            try {
              const dimensionInfo = getDimensionInfo(response.dimension);
              validation = await validateFeedbackRelevance(
                response.feedback,
                dimensionInfo.title,
                dimensionInfo.questions,
                project.title,
                project.description
              );
            } catch (error) {
              console.error(`AI validation failed for dimension ${response.dimension}:`, error);
            }
          }

          return {
            ...response,
            validation,
          };
        })
      );

      // Create review with responses in a transaction
      return ctx.prisma.$transaction(async (prisma) => {
        const review = await prisma.review.create({
          data: {
            userId: ctx.userId,
            projectId: input.projectId,
            overallSentiment: input.overallSentiment,
            overallComments: input.overallComments,
            overallCommentsRelevant: overallValidation?.isRelevant ?? true,
            overallRelevanceScore: overallValidation?.relevanceScore ?? 50,
            overallValidationReason: overallValidation?.reasoning,
          },
        });

        await prisma.reviewResponse.createMany({
          data: responsesWithValidation.map(response => ({
            reviewId: review.id,
            dimension: response.dimension,
            questionIndex: response.questionIndex,
            vote: response.vote,
            feedback: response.feedback,
            feedbackRelevant: response.validation?.isRelevant ?? true,
            relevanceScore: response.validation?.relevanceScore ?? 50,
            validationReason: response.validation?.reasoning,
            aiConfidence: response.validation?.confidence ?? 0,
          })),
        });

        return review;
      });
    }),

  getByProject: publicProcedure
    .input(z.object({ projectId: z.string() }))
    .query(async ({ ctx, input }) => {
      return ctx.prisma.review.findMany({
        where: {
          projectId: input.projectId,
        },
        include: {
          responses: true,
          user: {
            select: {
              id: true,
              name: true,
              isKYCVerified: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });
    }),

  getUserReviewForProject: publicProcedure
    .input(z.object({ projectId: z.string() }))
    .query(async ({ ctx, input }) => {
      return ctx.prisma.review.findUnique({
        where: {
          userId_projectId: {
            userId: ctx.userId,
            projectId: input.projectId,
          },
        },
        include: {
          responses: true,
        },
      });
    }),
});