import { z } from "zod";
import { publicProcedure, router } from "../lib/trpc";
import { analyzeProjectWithAI } from "../lib/ai-project-analysis";

export const aiAnalysisRouter = router({
  analyzeProject: publicProcedure
    .input(z.object({
      projectId: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      console.log("🤖 Starting AI analysis for project:", input.projectId);

      // Get project details
      const project = await ctx.prisma.project.findUnique({
        where: { id: input.projectId },
      });

      if (!project) {
        throw new Error("Project not found");
      }

      // Check if analysis already exists
      const existingAnalysis = await ctx.prisma.aIAnalysis.findUnique({
        where: { projectId: input.projectId },
      });

      if (existingAnalysis) {
        console.log("📊 Updating existing AI analysis");
      } else {
        console.log("🆕 Creating new AI analysis");
      }

      // Perform AI analysis
      const analysisResult = await analyzeProjectWithAI({
        title: project.title,
        description: project.description,
        websiteUrl: project.websiteUrl,
        deckUrl: project.deckUrl,
        whitepaperUrl: project.whitepaperUrl,
        socialUrls: project.socialUrls,
        challengeIntro: project.challengeIntro,
      });

      // Save or update analysis
      const aiAnalysis = await ctx.prisma.aIAnalysis.upsert({
        where: { projectId: input.projectId },
        update: {
          projectFundamentalsScore: analysisResult.projectFundamentalsScore,
          teamGovernanceScore: analysisResult.teamGovernanceScore,
          transparencyDocScore: analysisResult.transparencyDocScore,
          technologyExecutionScore: analysisResult.technologyExecutionScore,
          communityCommunicationScore: analysisResult.communityCommunicationScore,
          tokenUtilityTokenomicsScore: analysisResult.tokenUtilityTokenomicsScore,
          overallScore: analysisResult.overallScore,
          analysis: analysisResult.analysis,
          reasoning: analysisResult.reasoning,
          strengths: analysisResult.strengths,
          weaknesses: analysisResult.weaknesses,
          recommendations: analysisResult.recommendations,
          confidence: analysisResult.confidence,
          updatedAt: new Date(),
        },
        create: {
          projectId: input.projectId,
          projectFundamentalsScore: analysisResult.projectFundamentalsScore,
          teamGovernanceScore: analysisResult.teamGovernanceScore,
          transparencyDocScore: analysisResult.transparencyDocScore,
          technologyExecutionScore: analysisResult.technologyExecutionScore,
          communityCommunicationScore: analysisResult.communityCommunicationScore,
          tokenUtilityTokenomicsScore: analysisResult.tokenUtilityTokenomicsScore,
          overallScore: analysisResult.overallScore,
          analysis: analysisResult.analysis,
          reasoning: analysisResult.reasoning,
          strengths: analysisResult.strengths,
          weaknesses: analysisResult.weaknesses,
          recommendations: analysisResult.recommendations,
          confidence: analysisResult.confidence,
        },
      });

      console.log("✅ AI analysis saved successfully");
      return aiAnalysis;
    }),

  getProjectAnalysis: publicProcedure
    .input(z.object({
      projectId: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      return ctx.prisma.aIAnalysis.findUnique({
        where: { projectId: input.projectId },
      });
    }),

  getAllAnalyses: publicProcedure
    .query(async ({ ctx }) => {
      return ctx.prisma.aIAnalysis.findMany({
        include: {
          project: {
            select: {
              id: true,
              title: true,
              description: true,
            },
          },
        },
        orderBy: {
          updatedAt: "desc",
        },
      });
    }),

  deleteAnalysis: publicProcedure
    .input(z.object({
      projectId: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      console.log("🗑️ Deleting AI analysis for project:", input.projectId);

      const deleted = await ctx.prisma.aIAnalysis.delete({
        where: { projectId: input.projectId },
      });

      console.log("✅ AI analysis deleted successfully");
      return deleted;
    }),

  batchAnalyzeProjects: publicProcedure
    .input(z.object({
      projectIds: z.array(z.string()).optional(),
      analyzeAll: z.boolean().default(false),
    }))
    .mutation(async ({ ctx, input }) => {
      console.log("🔄 Starting batch AI analysis");

      let projectsToAnalyze;

      if (input.analyzeAll) {
        // Get all projects without AI analysis
        projectsToAnalyze = await ctx.prisma.project.findMany({
          where: {
            aiAnalysis: null,
          },
          select: {
            id: true,
            title: true,
            description: true,
            websiteUrl: true,
            deckUrl: true,
            whitepaperUrl: true,
            socialUrls: true,
            challengeIntro: true,
          },
        });
      } else if (input.projectIds) {
        // Get specific projects
        projectsToAnalyze = await ctx.prisma.project.findMany({
          where: {
            id: { in: input.projectIds },
          },
          select: {
            id: true,
            title: true,
            description: true,
            websiteUrl: true,
            deckUrl: true,
            whitepaperUrl: true,
            socialUrls: true,
            challengeIntro: true,
          },
        });
      } else {
        throw new Error("Either projectIds or analyzeAll must be provided");
      }

      console.log(`📊 Analyzing ${projectsToAnalyze.length} projects`);

      const results = [];
      const errors = [];

      for (const project of projectsToAnalyze) {
        try {
          console.log(`🤖 Analyzing project: ${project.title}`);

          const analysisResult = await analyzeProjectWithAI({
            title: project.title,
            description: project.description,
            websiteUrl: project.websiteUrl,
            deckUrl: project.deckUrl,
            whitepaperUrl: project.whitepaperUrl,
            socialUrls: project.socialUrls,
            challengeIntro: project.challengeIntro,
          });

          const aiAnalysis = await ctx.prisma.aIAnalysis.upsert({
            where: { projectId: project.id },
            update: {
              projectFundamentalsScore: analysisResult.projectFundamentalsScore,
              teamGovernanceScore: analysisResult.teamGovernanceScore,
              transparencyDocScore: analysisResult.transparencyDocScore,
              technologyExecutionScore: analysisResult.technologyExecutionScore,
              communityCommunicationScore: analysisResult.communityCommunicationScore,
              tokenUtilityTokenomicsScore: analysisResult.tokenUtilityTokenomicsScore,
              overallScore: analysisResult.overallScore,
              analysis: analysisResult.analysis,
              reasoning: analysisResult.reasoning,
              strengths: analysisResult.strengths,
              weaknesses: analysisResult.weaknesses,
              recommendations: analysisResult.recommendations,
              confidence: analysisResult.confidence,
              updatedAt: new Date(),
            },
            create: {
              projectId: project.id,
              projectFundamentalsScore: analysisResult.projectFundamentalsScore,
              teamGovernanceScore: analysisResult.teamGovernanceScore,
              transparencyDocScore: analysisResult.transparencyDocScore,
              technologyExecutionScore: analysisResult.technologyExecutionScore,
              communityCommunicationScore: analysisResult.communityCommunicationScore,
              tokenUtilityTokenomicsScore: analysisResult.tokenUtilityTokenomicsScore,
              overallScore: analysisResult.overallScore,
              analysis: analysisResult.analysis,
              reasoning: analysisResult.reasoning,
              strengths: analysisResult.strengths,
              weaknesses: analysisResult.weaknesses,
              recommendations: analysisResult.recommendations,
              confidence: analysisResult.confidence,
            },
          });

          results.push({
            projectId: project.id,
            projectTitle: project.title,
            overallScore: aiAnalysis.overallScore,
            confidence: aiAnalysis.confidence,
          });

          // Add delay to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 2000));

        } catch (error) {
          console.error(`❌ Failed to analyze project ${project.title}:`, error);
          errors.push({
            projectId: project.id,
            projectTitle: project.title,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }

      console.log(`✅ Batch analysis completed. Success: ${results.length}, Errors: ${errors.length}`);

      return {
        success: results,
        errors,
        totalProcessed: projectsToAnalyze.length,
      };
    }),
});
