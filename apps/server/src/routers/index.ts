import {
  publicProcedure,
  router,
} from "../lib/trpc";
import { projectsRouter } from "./projects";
import { reviewsRouter } from "./reviews";
import { usersRouter } from "./users";
import { aiAnalysisRouter } from "./ai-analysis";

export const appRouter = router({
  healthCheck: publicProcedure.query(() => {
    return "OK";
  }),
  projects: projectsRouter,
  reviews: reviewsRouter,
  users: usersRouter,
  aiAnalysis: aiAnalysisRouter,
});

export type AppRouter = typeof appRouter;
