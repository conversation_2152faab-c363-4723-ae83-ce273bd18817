import { z } from "zod";
import { publicProcedure, router } from "../lib/trpc";
import { REVIEW_DIMENSIONS, calculateDimensionScore } from "../lib/review-constants";

export const projectsRouter = router({
  list: publicProcedure.query(async ({ ctx }) => {
    return ctx.prisma.project.findMany({
      where: {
        isApprovedForVoting: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });
  }),

  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const project = await ctx.prisma.project.findUnique({
        where: { id: input.id },
        include: {
          reviews: {
            include: {
              responses: true,
              user: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          aiAnalysis: true,
        },
      });

      if (!project) {
        throw new Error("Project not found");
      }

      // Calculate aggregated dimension scores
      const dimensionScores = REVIEW_DIMENSIONS.map(dimension => {
        const allResponses = project.reviews.flatMap(review =>
          review.responses.filter(response => response.dimension === dimension.key)
        );

        const score = calculateDimensionScore(allResponses);
        
        return {
          dimension: dimension.key,
          title: dimension.title,
          score,
          totalReviews: project.reviews.length,
        };
      });

      // Create AI dimension scores if AI analysis exists
      const aiDimensionScores = project.aiAnalysis ? [
        {
          dimension: "PROJECT_FUNDAMENTALS",
          title: "Project Fundamentals",
          score: project.aiAnalysis.projectFundamentalsScore,
        },
        {
          dimension: "TEAM_GOVERNANCE",
          title: "Team & Governance",
          score: project.aiAnalysis.teamGovernanceScore,
        },
        {
          dimension: "TRANSPARENCY_DOCUMENTATION",
          title: "Transparency & Documentation",
          score: project.aiAnalysis.transparencyDocScore,
        },
        {
          dimension: "TECHNOLOGY_EXECUTION",
          title: "Technology & Execution",
          score: project.aiAnalysis.technologyExecutionScore,
        },
        {
          dimension: "COMMUNITY_COMMUNICATION",
          title: "Community & Communication",
          score: project.aiAnalysis.communityCommunicationScore,
        },
        {
          dimension: "TOKEN_UTILITY_TOKENOMICS",
          title: "Token Utility & Tokenomics",
          score: project.aiAnalysis.tokenUtilityTokenomicsScore,
        },
      ] : null;

      return {
        ...project,
        dimensionScores,
        aiDimensionScores,
        totalReviews: project.reviews.length,
      };
    }),

  create: publicProcedure
    .input(z.object({
      title: z.string().min(1),
      description: z.string().min(1),
      imageUrl: z.string().url().optional(),
      websiteUrl: z.string().url().optional(),
      deckUrl: z.string().url().optional(),
      whitepaperUrl: z.string().url().optional(),
      socialUrls: z.array(z.string().url()).optional(),
      challengeIntro: z.string().min(1),
    }))
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.project.create({
        data: {
          ...input,
          socialUrls: input.socialUrls || [],
        },
      });
    }),
});