import { z } from "zod";
import { publicProcedure, router } from "../lib/trpc";

export const usersRouter = router({
  getCurrentUser: publicProcedure.query(async ({ ctx }) => {
    let user = await ctx.prisma.user.findUnique({
      where: { id: ctx.userId },
    });

    // Auto-create user if doesn't exist (for development)
    if (!user) {
      user = await ctx.prisma.user.create({
        data: {
          id: ctx.userId,
          email: `${ctx.userId}@example.com`,
          name: `User ${ctx.userId}`,
          isKYCVerified: true, // Default to true for development
        },
      });
    }

    return user;
  }),

  updateKYCStatus: publicProcedure
    .input(z.object({
      userId: z.string(),
      isKYCVerified: z.boolean(),
    }))
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.user.update({
        where: { id: input.userId },
        data: { isKYCVerified: input.isKYCVerified },
      });
    }),
});