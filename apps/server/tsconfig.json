{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "composite": true, "incremental": true, "plugins": [{"name": "next"}], "types": ["node"], "paths": {"@/*": ["./src/*"], "prisma": ["./node_modules/prisma"]}, "declaration": true, "emitDeclarationOnly": true, "outDir": "./dist", "noEmit": true, "typeRoots": ["./node_modules/@types", "../../node_modules/@types"]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "dist"]}