
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
* This file exports all enum related types from the schema.
*
* 🟢 You can import this file directly.
*/
export const ReviewDimension = {
  PROJECT_FUNDAMENTALS: 'PROJECT_FUNDAMENTALS',
  TEAM_GOVERNANCE: 'TEAM_GOVERNANCE',
  TRANSPARENCY_DOCUMENTATION: 'TRANSPARENCY_DOCUMENTATION',
  TECHNOLOGY_EXECUTION: 'TECHNOLOGY_EXECUTION',
  COMMUNITY_COMMUNICATION: 'COMMUNITY_COMMUNICATION',
  TOKEN_UTILITY_TOKENOMICS: 'TOKEN_UTILITY_TOKENOMICS'
} as const

export type ReviewDimension = (typeof ReviewDimension)[keyof typeof ReviewDimension]
