
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Review` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model Review
 * 
 */
export type ReviewModel = runtime.Types.Result.DefaultSelection<Prisma.$ReviewPayload>

export type AggregateReview = {
  _count: ReviewCountAggregateOutputType | null
  _avg: ReviewAvgAggregateOutputType | null
  _sum: ReviewSumAggregateOutputType | null
  _min: ReviewMinAggregateOutputType | null
  _max: ReviewMaxAggregateOutputType | null
}

export type ReviewAvgAggregateOutputType = {
  overallRelevanceScore: number | null
}

export type ReviewSumAggregateOutputType = {
  overallRelevanceScore: number | null
}

export type ReviewMinAggregateOutputType = {
  id: string | null
  userId: string | null
  projectId: string | null
  overallSentiment: boolean | null
  overallComments: string | null
  overallCommentsRelevant: boolean | null
  overallRelevanceScore: number | null
  overallValidationReason: string | null
  createdAt: Date | null
}

export type ReviewMaxAggregateOutputType = {
  id: string | null
  userId: string | null
  projectId: string | null
  overallSentiment: boolean | null
  overallComments: string | null
  overallCommentsRelevant: boolean | null
  overallRelevanceScore: number | null
  overallValidationReason: string | null
  createdAt: Date | null
}

export type ReviewCountAggregateOutputType = {
  id: number
  userId: number
  projectId: number
  overallSentiment: number
  overallComments: number
  overallCommentsRelevant: number
  overallRelevanceScore: number
  overallValidationReason: number
  createdAt: number
  _all: number
}


export type ReviewAvgAggregateInputType = {
  overallRelevanceScore?: true
}

export type ReviewSumAggregateInputType = {
  overallRelevanceScore?: true
}

export type ReviewMinAggregateInputType = {
  id?: true
  userId?: true
  projectId?: true
  overallSentiment?: true
  overallComments?: true
  overallCommentsRelevant?: true
  overallRelevanceScore?: true
  overallValidationReason?: true
  createdAt?: true
}

export type ReviewMaxAggregateInputType = {
  id?: true
  userId?: true
  projectId?: true
  overallSentiment?: true
  overallComments?: true
  overallCommentsRelevant?: true
  overallRelevanceScore?: true
  overallValidationReason?: true
  createdAt?: true
}

export type ReviewCountAggregateInputType = {
  id?: true
  userId?: true
  projectId?: true
  overallSentiment?: true
  overallComments?: true
  overallCommentsRelevant?: true
  overallRelevanceScore?: true
  overallValidationReason?: true
  createdAt?: true
  _all?: true
}

export type ReviewAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Review to aggregate.
   */
  where?: Prisma.ReviewWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Reviews to fetch.
   */
  orderBy?: Prisma.ReviewOrderByWithRelationInput | Prisma.ReviewOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.ReviewWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Reviews from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Reviews.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Reviews
  **/
  _count?: true | ReviewCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: ReviewAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: ReviewSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: ReviewMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: ReviewMaxAggregateInputType
}

export type GetReviewAggregateType<T extends ReviewAggregateArgs> = {
      [P in keyof T & keyof AggregateReview]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateReview[P]>
    : Prisma.GetScalarType<T[P], AggregateReview[P]>
}




export type ReviewGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ReviewWhereInput
  orderBy?: Prisma.ReviewOrderByWithAggregationInput | Prisma.ReviewOrderByWithAggregationInput[]
  by: Prisma.ReviewScalarFieldEnum[] | Prisma.ReviewScalarFieldEnum
  having?: Prisma.ReviewScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: ReviewCountAggregateInputType | true
  _avg?: ReviewAvgAggregateInputType
  _sum?: ReviewSumAggregateInputType
  _min?: ReviewMinAggregateInputType
  _max?: ReviewMaxAggregateInputType
}

export type ReviewGroupByOutputType = {
  id: string
  userId: string
  projectId: string
  overallSentiment: boolean
  overallComments: string | null
  overallCommentsRelevant: boolean
  overallRelevanceScore: number
  overallValidationReason: string | null
  createdAt: Date
  _count: ReviewCountAggregateOutputType | null
  _avg: ReviewAvgAggregateOutputType | null
  _sum: ReviewSumAggregateOutputType | null
  _min: ReviewMinAggregateOutputType | null
  _max: ReviewMaxAggregateOutputType | null
}

type GetReviewGroupByPayload<T extends ReviewGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<ReviewGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof ReviewGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], ReviewGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], ReviewGroupByOutputType[P]>
      }
    >
  > 



export type ReviewWhereInput = {
  AND?: Prisma.ReviewWhereInput | Prisma.ReviewWhereInput[]
  OR?: Prisma.ReviewWhereInput[]
  NOT?: Prisma.ReviewWhereInput | Prisma.ReviewWhereInput[]
  id?: Prisma.StringFilter<"Review"> | string
  userId?: Prisma.StringFilter<"Review"> | string
  projectId?: Prisma.StringFilter<"Review"> | string
  overallSentiment?: Prisma.BoolFilter<"Review"> | boolean
  overallComments?: Prisma.StringNullableFilter<"Review"> | string | null
  overallCommentsRelevant?: Prisma.BoolFilter<"Review"> | boolean
  overallRelevanceScore?: Prisma.IntFilter<"Review"> | number
  overallValidationReason?: Prisma.StringNullableFilter<"Review"> | string | null
  createdAt?: Prisma.DateTimeFilter<"Review"> | Date | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  project?: Prisma.XOR<Prisma.ProjectScalarRelationFilter, Prisma.ProjectWhereInput>
  responses?: Prisma.ReviewResponseListRelationFilter
}

export type ReviewOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  projectId?: Prisma.SortOrder
  overallSentiment?: Prisma.SortOrder
  overallComments?: Prisma.SortOrderInput | Prisma.SortOrder
  overallCommentsRelevant?: Prisma.SortOrder
  overallRelevanceScore?: Prisma.SortOrder
  overallValidationReason?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  user?: Prisma.UserOrderByWithRelationInput
  project?: Prisma.ProjectOrderByWithRelationInput
  responses?: Prisma.ReviewResponseOrderByRelationAggregateInput
}

export type ReviewWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  userId_projectId?: Prisma.ReviewUserIdProjectIdCompoundUniqueInput
  AND?: Prisma.ReviewWhereInput | Prisma.ReviewWhereInput[]
  OR?: Prisma.ReviewWhereInput[]
  NOT?: Prisma.ReviewWhereInput | Prisma.ReviewWhereInput[]
  userId?: Prisma.StringFilter<"Review"> | string
  projectId?: Prisma.StringFilter<"Review"> | string
  overallSentiment?: Prisma.BoolFilter<"Review"> | boolean
  overallComments?: Prisma.StringNullableFilter<"Review"> | string | null
  overallCommentsRelevant?: Prisma.BoolFilter<"Review"> | boolean
  overallRelevanceScore?: Prisma.IntFilter<"Review"> | number
  overallValidationReason?: Prisma.StringNullableFilter<"Review"> | string | null
  createdAt?: Prisma.DateTimeFilter<"Review"> | Date | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  project?: Prisma.XOR<Prisma.ProjectScalarRelationFilter, Prisma.ProjectWhereInput>
  responses?: Prisma.ReviewResponseListRelationFilter
}, "id" | "userId_projectId">

export type ReviewOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  projectId?: Prisma.SortOrder
  overallSentiment?: Prisma.SortOrder
  overallComments?: Prisma.SortOrderInput | Prisma.SortOrder
  overallCommentsRelevant?: Prisma.SortOrder
  overallRelevanceScore?: Prisma.SortOrder
  overallValidationReason?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  _count?: Prisma.ReviewCountOrderByAggregateInput
  _avg?: Prisma.ReviewAvgOrderByAggregateInput
  _max?: Prisma.ReviewMaxOrderByAggregateInput
  _min?: Prisma.ReviewMinOrderByAggregateInput
  _sum?: Prisma.ReviewSumOrderByAggregateInput
}

export type ReviewScalarWhereWithAggregatesInput = {
  AND?: Prisma.ReviewScalarWhereWithAggregatesInput | Prisma.ReviewScalarWhereWithAggregatesInput[]
  OR?: Prisma.ReviewScalarWhereWithAggregatesInput[]
  NOT?: Prisma.ReviewScalarWhereWithAggregatesInput | Prisma.ReviewScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Review"> | string
  userId?: Prisma.StringWithAggregatesFilter<"Review"> | string
  projectId?: Prisma.StringWithAggregatesFilter<"Review"> | string
  overallSentiment?: Prisma.BoolWithAggregatesFilter<"Review"> | boolean
  overallComments?: Prisma.StringNullableWithAggregatesFilter<"Review"> | string | null
  overallCommentsRelevant?: Prisma.BoolWithAggregatesFilter<"Review"> | boolean
  overallRelevanceScore?: Prisma.IntWithAggregatesFilter<"Review"> | number
  overallValidationReason?: Prisma.StringNullableWithAggregatesFilter<"Review"> | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Review"> | Date | string
}

export type ReviewCreateInput = {
  id?: string
  overallSentiment: boolean
  overallComments?: string | null
  overallCommentsRelevant?: boolean
  overallRelevanceScore?: number
  overallValidationReason?: string | null
  createdAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutReviewsInput
  project: Prisma.ProjectCreateNestedOneWithoutReviewsInput
  responses?: Prisma.ReviewResponseCreateNestedManyWithoutReviewInput
}

export type ReviewUncheckedCreateInput = {
  id?: string
  userId: string
  projectId: string
  overallSentiment: boolean
  overallComments?: string | null
  overallCommentsRelevant?: boolean
  overallRelevanceScore?: number
  overallValidationReason?: string | null
  createdAt?: Date | string
  responses?: Prisma.ReviewResponseUncheckedCreateNestedManyWithoutReviewInput
}

export type ReviewUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  overallSentiment?: Prisma.BoolFieldUpdateOperationsInput | boolean
  overallComments?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  overallCommentsRelevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
  overallRelevanceScore?: Prisma.IntFieldUpdateOperationsInput | number
  overallValidationReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutReviewsNestedInput
  project?: Prisma.ProjectUpdateOneRequiredWithoutReviewsNestedInput
  responses?: Prisma.ReviewResponseUpdateManyWithoutReviewNestedInput
}

export type ReviewUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  projectId?: Prisma.StringFieldUpdateOperationsInput | string
  overallSentiment?: Prisma.BoolFieldUpdateOperationsInput | boolean
  overallComments?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  overallCommentsRelevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
  overallRelevanceScore?: Prisma.IntFieldUpdateOperationsInput | number
  overallValidationReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  responses?: Prisma.ReviewResponseUncheckedUpdateManyWithoutReviewNestedInput
}

export type ReviewCreateManyInput = {
  id?: string
  userId: string
  projectId: string
  overallSentiment: boolean
  overallComments?: string | null
  overallCommentsRelevant?: boolean
  overallRelevanceScore?: number
  overallValidationReason?: string | null
  createdAt?: Date | string
}

export type ReviewUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  overallSentiment?: Prisma.BoolFieldUpdateOperationsInput | boolean
  overallComments?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  overallCommentsRelevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
  overallRelevanceScore?: Prisma.IntFieldUpdateOperationsInput | number
  overallValidationReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ReviewUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  projectId?: Prisma.StringFieldUpdateOperationsInput | string
  overallSentiment?: Prisma.BoolFieldUpdateOperationsInput | boolean
  overallComments?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  overallCommentsRelevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
  overallRelevanceScore?: Prisma.IntFieldUpdateOperationsInput | number
  overallValidationReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ReviewListRelationFilter = {
  every?: Prisma.ReviewWhereInput
  some?: Prisma.ReviewWhereInput
  none?: Prisma.ReviewWhereInput
}

export type ReviewOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type ReviewUserIdProjectIdCompoundUniqueInput = {
  userId: string
  projectId: string
}

export type ReviewCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  projectId?: Prisma.SortOrder
  overallSentiment?: Prisma.SortOrder
  overallComments?: Prisma.SortOrder
  overallCommentsRelevant?: Prisma.SortOrder
  overallRelevanceScore?: Prisma.SortOrder
  overallValidationReason?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type ReviewAvgOrderByAggregateInput = {
  overallRelevanceScore?: Prisma.SortOrder
}

export type ReviewMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  projectId?: Prisma.SortOrder
  overallSentiment?: Prisma.SortOrder
  overallComments?: Prisma.SortOrder
  overallCommentsRelevant?: Prisma.SortOrder
  overallRelevanceScore?: Prisma.SortOrder
  overallValidationReason?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type ReviewMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  projectId?: Prisma.SortOrder
  overallSentiment?: Prisma.SortOrder
  overallComments?: Prisma.SortOrder
  overallCommentsRelevant?: Prisma.SortOrder
  overallRelevanceScore?: Prisma.SortOrder
  overallValidationReason?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type ReviewSumOrderByAggregateInput = {
  overallRelevanceScore?: Prisma.SortOrder
}

export type ReviewScalarRelationFilter = {
  is?: Prisma.ReviewWhereInput
  isNot?: Prisma.ReviewWhereInput
}

export type ReviewCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.ReviewCreateWithoutUserInput, Prisma.ReviewUncheckedCreateWithoutUserInput> | Prisma.ReviewCreateWithoutUserInput[] | Prisma.ReviewUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.ReviewCreateOrConnectWithoutUserInput | Prisma.ReviewCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.ReviewCreateManyUserInputEnvelope
  connect?: Prisma.ReviewWhereUniqueInput | Prisma.ReviewWhereUniqueInput[]
}

export type ReviewUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.ReviewCreateWithoutUserInput, Prisma.ReviewUncheckedCreateWithoutUserInput> | Prisma.ReviewCreateWithoutUserInput[] | Prisma.ReviewUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.ReviewCreateOrConnectWithoutUserInput | Prisma.ReviewCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.ReviewCreateManyUserInputEnvelope
  connect?: Prisma.ReviewWhereUniqueInput | Prisma.ReviewWhereUniqueInput[]
}

export type ReviewUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.ReviewCreateWithoutUserInput, Prisma.ReviewUncheckedCreateWithoutUserInput> | Prisma.ReviewCreateWithoutUserInput[] | Prisma.ReviewUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.ReviewCreateOrConnectWithoutUserInput | Prisma.ReviewCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.ReviewUpsertWithWhereUniqueWithoutUserInput | Prisma.ReviewUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.ReviewCreateManyUserInputEnvelope
  set?: Prisma.ReviewWhereUniqueInput | Prisma.ReviewWhereUniqueInput[]
  disconnect?: Prisma.ReviewWhereUniqueInput | Prisma.ReviewWhereUniqueInput[]
  delete?: Prisma.ReviewWhereUniqueInput | Prisma.ReviewWhereUniqueInput[]
  connect?: Prisma.ReviewWhereUniqueInput | Prisma.ReviewWhereUniqueInput[]
  update?: Prisma.ReviewUpdateWithWhereUniqueWithoutUserInput | Prisma.ReviewUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.ReviewUpdateManyWithWhereWithoutUserInput | Prisma.ReviewUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.ReviewScalarWhereInput | Prisma.ReviewScalarWhereInput[]
}

export type ReviewUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.ReviewCreateWithoutUserInput, Prisma.ReviewUncheckedCreateWithoutUserInput> | Prisma.ReviewCreateWithoutUserInput[] | Prisma.ReviewUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.ReviewCreateOrConnectWithoutUserInput | Prisma.ReviewCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.ReviewUpsertWithWhereUniqueWithoutUserInput | Prisma.ReviewUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.ReviewCreateManyUserInputEnvelope
  set?: Prisma.ReviewWhereUniqueInput | Prisma.ReviewWhereUniqueInput[]
  disconnect?: Prisma.ReviewWhereUniqueInput | Prisma.ReviewWhereUniqueInput[]
  delete?: Prisma.ReviewWhereUniqueInput | Prisma.ReviewWhereUniqueInput[]
  connect?: Prisma.ReviewWhereUniqueInput | Prisma.ReviewWhereUniqueInput[]
  update?: Prisma.ReviewUpdateWithWhereUniqueWithoutUserInput | Prisma.ReviewUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.ReviewUpdateManyWithWhereWithoutUserInput | Prisma.ReviewUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.ReviewScalarWhereInput | Prisma.ReviewScalarWhereInput[]
}

export type ReviewCreateNestedManyWithoutProjectInput = {
  create?: Prisma.XOR<Prisma.ReviewCreateWithoutProjectInput, Prisma.ReviewUncheckedCreateWithoutProjectInput> | Prisma.ReviewCreateWithoutProjectInput[] | Prisma.ReviewUncheckedCreateWithoutProjectInput[]
  connectOrCreate?: Prisma.ReviewCreateOrConnectWithoutProjectInput | Prisma.ReviewCreateOrConnectWithoutProjectInput[]
  createMany?: Prisma.ReviewCreateManyProjectInputEnvelope
  connect?: Prisma.ReviewWhereUniqueInput | Prisma.ReviewWhereUniqueInput[]
}

export type ReviewUncheckedCreateNestedManyWithoutProjectInput = {
  create?: Prisma.XOR<Prisma.ReviewCreateWithoutProjectInput, Prisma.ReviewUncheckedCreateWithoutProjectInput> | Prisma.ReviewCreateWithoutProjectInput[] | Prisma.ReviewUncheckedCreateWithoutProjectInput[]
  connectOrCreate?: Prisma.ReviewCreateOrConnectWithoutProjectInput | Prisma.ReviewCreateOrConnectWithoutProjectInput[]
  createMany?: Prisma.ReviewCreateManyProjectInputEnvelope
  connect?: Prisma.ReviewWhereUniqueInput | Prisma.ReviewWhereUniqueInput[]
}

export type ReviewUpdateManyWithoutProjectNestedInput = {
  create?: Prisma.XOR<Prisma.ReviewCreateWithoutProjectInput, Prisma.ReviewUncheckedCreateWithoutProjectInput> | Prisma.ReviewCreateWithoutProjectInput[] | Prisma.ReviewUncheckedCreateWithoutProjectInput[]
  connectOrCreate?: Prisma.ReviewCreateOrConnectWithoutProjectInput | Prisma.ReviewCreateOrConnectWithoutProjectInput[]
  upsert?: Prisma.ReviewUpsertWithWhereUniqueWithoutProjectInput | Prisma.ReviewUpsertWithWhereUniqueWithoutProjectInput[]
  createMany?: Prisma.ReviewCreateManyProjectInputEnvelope
  set?: Prisma.ReviewWhereUniqueInput | Prisma.ReviewWhereUniqueInput[]
  disconnect?: Prisma.ReviewWhereUniqueInput | Prisma.ReviewWhereUniqueInput[]
  delete?: Prisma.ReviewWhereUniqueInput | Prisma.ReviewWhereUniqueInput[]
  connect?: Prisma.ReviewWhereUniqueInput | Prisma.ReviewWhereUniqueInput[]
  update?: Prisma.ReviewUpdateWithWhereUniqueWithoutProjectInput | Prisma.ReviewUpdateWithWhereUniqueWithoutProjectInput[]
  updateMany?: Prisma.ReviewUpdateManyWithWhereWithoutProjectInput | Prisma.ReviewUpdateManyWithWhereWithoutProjectInput[]
  deleteMany?: Prisma.ReviewScalarWhereInput | Prisma.ReviewScalarWhereInput[]
}

export type ReviewUncheckedUpdateManyWithoutProjectNestedInput = {
  create?: Prisma.XOR<Prisma.ReviewCreateWithoutProjectInput, Prisma.ReviewUncheckedCreateWithoutProjectInput> | Prisma.ReviewCreateWithoutProjectInput[] | Prisma.ReviewUncheckedCreateWithoutProjectInput[]
  connectOrCreate?: Prisma.ReviewCreateOrConnectWithoutProjectInput | Prisma.ReviewCreateOrConnectWithoutProjectInput[]
  upsert?: Prisma.ReviewUpsertWithWhereUniqueWithoutProjectInput | Prisma.ReviewUpsertWithWhereUniqueWithoutProjectInput[]
  createMany?: Prisma.ReviewCreateManyProjectInputEnvelope
  set?: Prisma.ReviewWhereUniqueInput | Prisma.ReviewWhereUniqueInput[]
  disconnect?: Prisma.ReviewWhereUniqueInput | Prisma.ReviewWhereUniqueInput[]
  delete?: Prisma.ReviewWhereUniqueInput | Prisma.ReviewWhereUniqueInput[]
  connect?: Prisma.ReviewWhereUniqueInput | Prisma.ReviewWhereUniqueInput[]
  update?: Prisma.ReviewUpdateWithWhereUniqueWithoutProjectInput | Prisma.ReviewUpdateWithWhereUniqueWithoutProjectInput[]
  updateMany?: Prisma.ReviewUpdateManyWithWhereWithoutProjectInput | Prisma.ReviewUpdateManyWithWhereWithoutProjectInput[]
  deleteMany?: Prisma.ReviewScalarWhereInput | Prisma.ReviewScalarWhereInput[]
}

export type IntFieldUpdateOperationsInput = {
  set?: number
  increment?: number
  decrement?: number
  multiply?: number
  divide?: number
}

export type ReviewCreateNestedOneWithoutResponsesInput = {
  create?: Prisma.XOR<Prisma.ReviewCreateWithoutResponsesInput, Prisma.ReviewUncheckedCreateWithoutResponsesInput>
  connectOrCreate?: Prisma.ReviewCreateOrConnectWithoutResponsesInput
  connect?: Prisma.ReviewWhereUniqueInput
}

export type ReviewUpdateOneRequiredWithoutResponsesNestedInput = {
  create?: Prisma.XOR<Prisma.ReviewCreateWithoutResponsesInput, Prisma.ReviewUncheckedCreateWithoutResponsesInput>
  connectOrCreate?: Prisma.ReviewCreateOrConnectWithoutResponsesInput
  upsert?: Prisma.ReviewUpsertWithoutResponsesInput
  connect?: Prisma.ReviewWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.ReviewUpdateToOneWithWhereWithoutResponsesInput, Prisma.ReviewUpdateWithoutResponsesInput>, Prisma.ReviewUncheckedUpdateWithoutResponsesInput>
}

export type ReviewCreateWithoutUserInput = {
  id?: string
  overallSentiment: boolean
  overallComments?: string | null
  overallCommentsRelevant?: boolean
  overallRelevanceScore?: number
  overallValidationReason?: string | null
  createdAt?: Date | string
  project: Prisma.ProjectCreateNestedOneWithoutReviewsInput
  responses?: Prisma.ReviewResponseCreateNestedManyWithoutReviewInput
}

export type ReviewUncheckedCreateWithoutUserInput = {
  id?: string
  projectId: string
  overallSentiment: boolean
  overallComments?: string | null
  overallCommentsRelevant?: boolean
  overallRelevanceScore?: number
  overallValidationReason?: string | null
  createdAt?: Date | string
  responses?: Prisma.ReviewResponseUncheckedCreateNestedManyWithoutReviewInput
}

export type ReviewCreateOrConnectWithoutUserInput = {
  where: Prisma.ReviewWhereUniqueInput
  create: Prisma.XOR<Prisma.ReviewCreateWithoutUserInput, Prisma.ReviewUncheckedCreateWithoutUserInput>
}

export type ReviewCreateManyUserInputEnvelope = {
  data: Prisma.ReviewCreateManyUserInput | Prisma.ReviewCreateManyUserInput[]
}

export type ReviewUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.ReviewWhereUniqueInput
  update: Prisma.XOR<Prisma.ReviewUpdateWithoutUserInput, Prisma.ReviewUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.ReviewCreateWithoutUserInput, Prisma.ReviewUncheckedCreateWithoutUserInput>
}

export type ReviewUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.ReviewWhereUniqueInput
  data: Prisma.XOR<Prisma.ReviewUpdateWithoutUserInput, Prisma.ReviewUncheckedUpdateWithoutUserInput>
}

export type ReviewUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.ReviewScalarWhereInput
  data: Prisma.XOR<Prisma.ReviewUpdateManyMutationInput, Prisma.ReviewUncheckedUpdateManyWithoutUserInput>
}

export type ReviewScalarWhereInput = {
  AND?: Prisma.ReviewScalarWhereInput | Prisma.ReviewScalarWhereInput[]
  OR?: Prisma.ReviewScalarWhereInput[]
  NOT?: Prisma.ReviewScalarWhereInput | Prisma.ReviewScalarWhereInput[]
  id?: Prisma.StringFilter<"Review"> | string
  userId?: Prisma.StringFilter<"Review"> | string
  projectId?: Prisma.StringFilter<"Review"> | string
  overallSentiment?: Prisma.BoolFilter<"Review"> | boolean
  overallComments?: Prisma.StringNullableFilter<"Review"> | string | null
  overallCommentsRelevant?: Prisma.BoolFilter<"Review"> | boolean
  overallRelevanceScore?: Prisma.IntFilter<"Review"> | number
  overallValidationReason?: Prisma.StringNullableFilter<"Review"> | string | null
  createdAt?: Prisma.DateTimeFilter<"Review"> | Date | string
}

export type ReviewCreateWithoutProjectInput = {
  id?: string
  overallSentiment: boolean
  overallComments?: string | null
  overallCommentsRelevant?: boolean
  overallRelevanceScore?: number
  overallValidationReason?: string | null
  createdAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutReviewsInput
  responses?: Prisma.ReviewResponseCreateNestedManyWithoutReviewInput
}

export type ReviewUncheckedCreateWithoutProjectInput = {
  id?: string
  userId: string
  overallSentiment: boolean
  overallComments?: string | null
  overallCommentsRelevant?: boolean
  overallRelevanceScore?: number
  overallValidationReason?: string | null
  createdAt?: Date | string
  responses?: Prisma.ReviewResponseUncheckedCreateNestedManyWithoutReviewInput
}

export type ReviewCreateOrConnectWithoutProjectInput = {
  where: Prisma.ReviewWhereUniqueInput
  create: Prisma.XOR<Prisma.ReviewCreateWithoutProjectInput, Prisma.ReviewUncheckedCreateWithoutProjectInput>
}

export type ReviewCreateManyProjectInputEnvelope = {
  data: Prisma.ReviewCreateManyProjectInput | Prisma.ReviewCreateManyProjectInput[]
}

export type ReviewUpsertWithWhereUniqueWithoutProjectInput = {
  where: Prisma.ReviewWhereUniqueInput
  update: Prisma.XOR<Prisma.ReviewUpdateWithoutProjectInput, Prisma.ReviewUncheckedUpdateWithoutProjectInput>
  create: Prisma.XOR<Prisma.ReviewCreateWithoutProjectInput, Prisma.ReviewUncheckedCreateWithoutProjectInput>
}

export type ReviewUpdateWithWhereUniqueWithoutProjectInput = {
  where: Prisma.ReviewWhereUniqueInput
  data: Prisma.XOR<Prisma.ReviewUpdateWithoutProjectInput, Prisma.ReviewUncheckedUpdateWithoutProjectInput>
}

export type ReviewUpdateManyWithWhereWithoutProjectInput = {
  where: Prisma.ReviewScalarWhereInput
  data: Prisma.XOR<Prisma.ReviewUpdateManyMutationInput, Prisma.ReviewUncheckedUpdateManyWithoutProjectInput>
}

export type ReviewCreateWithoutResponsesInput = {
  id?: string
  overallSentiment: boolean
  overallComments?: string | null
  overallCommentsRelevant?: boolean
  overallRelevanceScore?: number
  overallValidationReason?: string | null
  createdAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutReviewsInput
  project: Prisma.ProjectCreateNestedOneWithoutReviewsInput
}

export type ReviewUncheckedCreateWithoutResponsesInput = {
  id?: string
  userId: string
  projectId: string
  overallSentiment: boolean
  overallComments?: string | null
  overallCommentsRelevant?: boolean
  overallRelevanceScore?: number
  overallValidationReason?: string | null
  createdAt?: Date | string
}

export type ReviewCreateOrConnectWithoutResponsesInput = {
  where: Prisma.ReviewWhereUniqueInput
  create: Prisma.XOR<Prisma.ReviewCreateWithoutResponsesInput, Prisma.ReviewUncheckedCreateWithoutResponsesInput>
}

export type ReviewUpsertWithoutResponsesInput = {
  update: Prisma.XOR<Prisma.ReviewUpdateWithoutResponsesInput, Prisma.ReviewUncheckedUpdateWithoutResponsesInput>
  create: Prisma.XOR<Prisma.ReviewCreateWithoutResponsesInput, Prisma.ReviewUncheckedCreateWithoutResponsesInput>
  where?: Prisma.ReviewWhereInput
}

export type ReviewUpdateToOneWithWhereWithoutResponsesInput = {
  where?: Prisma.ReviewWhereInput
  data: Prisma.XOR<Prisma.ReviewUpdateWithoutResponsesInput, Prisma.ReviewUncheckedUpdateWithoutResponsesInput>
}

export type ReviewUpdateWithoutResponsesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  overallSentiment?: Prisma.BoolFieldUpdateOperationsInput | boolean
  overallComments?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  overallCommentsRelevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
  overallRelevanceScore?: Prisma.IntFieldUpdateOperationsInput | number
  overallValidationReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutReviewsNestedInput
  project?: Prisma.ProjectUpdateOneRequiredWithoutReviewsNestedInput
}

export type ReviewUncheckedUpdateWithoutResponsesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  projectId?: Prisma.StringFieldUpdateOperationsInput | string
  overallSentiment?: Prisma.BoolFieldUpdateOperationsInput | boolean
  overallComments?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  overallCommentsRelevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
  overallRelevanceScore?: Prisma.IntFieldUpdateOperationsInput | number
  overallValidationReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ReviewCreateManyUserInput = {
  id?: string
  projectId: string
  overallSentiment: boolean
  overallComments?: string | null
  overallCommentsRelevant?: boolean
  overallRelevanceScore?: number
  overallValidationReason?: string | null
  createdAt?: Date | string
}

export type ReviewUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  overallSentiment?: Prisma.BoolFieldUpdateOperationsInput | boolean
  overallComments?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  overallCommentsRelevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
  overallRelevanceScore?: Prisma.IntFieldUpdateOperationsInput | number
  overallValidationReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  project?: Prisma.ProjectUpdateOneRequiredWithoutReviewsNestedInput
  responses?: Prisma.ReviewResponseUpdateManyWithoutReviewNestedInput
}

export type ReviewUncheckedUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  projectId?: Prisma.StringFieldUpdateOperationsInput | string
  overallSentiment?: Prisma.BoolFieldUpdateOperationsInput | boolean
  overallComments?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  overallCommentsRelevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
  overallRelevanceScore?: Prisma.IntFieldUpdateOperationsInput | number
  overallValidationReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  responses?: Prisma.ReviewResponseUncheckedUpdateManyWithoutReviewNestedInput
}

export type ReviewUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  projectId?: Prisma.StringFieldUpdateOperationsInput | string
  overallSentiment?: Prisma.BoolFieldUpdateOperationsInput | boolean
  overallComments?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  overallCommentsRelevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
  overallRelevanceScore?: Prisma.IntFieldUpdateOperationsInput | number
  overallValidationReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ReviewCreateManyProjectInput = {
  id?: string
  userId: string
  overallSentiment: boolean
  overallComments?: string | null
  overallCommentsRelevant?: boolean
  overallRelevanceScore?: number
  overallValidationReason?: string | null
  createdAt?: Date | string
}

export type ReviewUpdateWithoutProjectInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  overallSentiment?: Prisma.BoolFieldUpdateOperationsInput | boolean
  overallComments?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  overallCommentsRelevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
  overallRelevanceScore?: Prisma.IntFieldUpdateOperationsInput | number
  overallValidationReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutReviewsNestedInput
  responses?: Prisma.ReviewResponseUpdateManyWithoutReviewNestedInput
}

export type ReviewUncheckedUpdateWithoutProjectInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  overallSentiment?: Prisma.BoolFieldUpdateOperationsInput | boolean
  overallComments?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  overallCommentsRelevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
  overallRelevanceScore?: Prisma.IntFieldUpdateOperationsInput | number
  overallValidationReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  responses?: Prisma.ReviewResponseUncheckedUpdateManyWithoutReviewNestedInput
}

export type ReviewUncheckedUpdateManyWithoutProjectInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  overallSentiment?: Prisma.BoolFieldUpdateOperationsInput | boolean
  overallComments?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  overallCommentsRelevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
  overallRelevanceScore?: Prisma.IntFieldUpdateOperationsInput | number
  overallValidationReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type ReviewCountOutputType
 */

export type ReviewCountOutputType = {
  responses: number
}

export type ReviewCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  responses?: boolean | ReviewCountOutputTypeCountResponsesArgs
}

/**
 * ReviewCountOutputType without action
 */
export type ReviewCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReviewCountOutputType
   */
  select?: Prisma.ReviewCountOutputTypeSelect<ExtArgs> | null
}

/**
 * ReviewCountOutputType without action
 */
export type ReviewCountOutputTypeCountResponsesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ReviewResponseWhereInput
}


export type ReviewSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  userId?: boolean
  projectId?: boolean
  overallSentiment?: boolean
  overallComments?: boolean
  overallCommentsRelevant?: boolean
  overallRelevanceScore?: boolean
  overallValidationReason?: boolean
  createdAt?: boolean
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  project?: boolean | Prisma.ProjectDefaultArgs<ExtArgs>
  responses?: boolean | Prisma.Review$responsesArgs<ExtArgs>
  _count?: boolean | Prisma.ReviewCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["review"]>

export type ReviewSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  userId?: boolean
  projectId?: boolean
  overallSentiment?: boolean
  overallComments?: boolean
  overallCommentsRelevant?: boolean
  overallRelevanceScore?: boolean
  overallValidationReason?: boolean
  createdAt?: boolean
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  project?: boolean | Prisma.ProjectDefaultArgs<ExtArgs>
}, ExtArgs["result"]["review"]>

export type ReviewSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  userId?: boolean
  projectId?: boolean
  overallSentiment?: boolean
  overallComments?: boolean
  overallCommentsRelevant?: boolean
  overallRelevanceScore?: boolean
  overallValidationReason?: boolean
  createdAt?: boolean
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  project?: boolean | Prisma.ProjectDefaultArgs<ExtArgs>
}, ExtArgs["result"]["review"]>

export type ReviewSelectScalar = {
  id?: boolean
  userId?: boolean
  projectId?: boolean
  overallSentiment?: boolean
  overallComments?: boolean
  overallCommentsRelevant?: boolean
  overallRelevanceScore?: boolean
  overallValidationReason?: boolean
  createdAt?: boolean
}

export type ReviewOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "userId" | "projectId" | "overallSentiment" | "overallComments" | "overallCommentsRelevant" | "overallRelevanceScore" | "overallValidationReason" | "createdAt", ExtArgs["result"]["review"]>
export type ReviewInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  project?: boolean | Prisma.ProjectDefaultArgs<ExtArgs>
  responses?: boolean | Prisma.Review$responsesArgs<ExtArgs>
  _count?: boolean | Prisma.ReviewCountOutputTypeDefaultArgs<ExtArgs>
}
export type ReviewIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  project?: boolean | Prisma.ProjectDefaultArgs<ExtArgs>
}
export type ReviewIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  project?: boolean | Prisma.ProjectDefaultArgs<ExtArgs>
}

export type $ReviewPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Review"
  objects: {
    user: Prisma.$UserPayload<ExtArgs>
    project: Prisma.$ProjectPayload<ExtArgs>
    responses: Prisma.$ReviewResponsePayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    userId: string
    projectId: string
    overallSentiment: boolean
    overallComments: string | null
    overallCommentsRelevant: boolean
    overallRelevanceScore: number
    overallValidationReason: string | null
    createdAt: Date
  }, ExtArgs["result"]["review"]>
  composites: {}
}

export type ReviewGetPayload<S extends boolean | null | undefined | ReviewDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$ReviewPayload, S>

export type ReviewCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<ReviewFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: ReviewCountAggregateInputType | true
  }

export interface ReviewDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Review'], meta: { name: 'Review' } }
  /**
   * Find zero or one Review that matches the filter.
   * @param {ReviewFindUniqueArgs} args - Arguments to find a Review
   * @example
   * // Get one Review
   * const review = await prisma.review.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends ReviewFindUniqueArgs>(args: Prisma.SelectSubset<T, ReviewFindUniqueArgs<ExtArgs>>): Prisma.Prisma__ReviewClient<runtime.Types.Result.GetResult<Prisma.$ReviewPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Review that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {ReviewFindUniqueOrThrowArgs} args - Arguments to find a Review
   * @example
   * // Get one Review
   * const review = await prisma.review.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends ReviewFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, ReviewFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__ReviewClient<runtime.Types.Result.GetResult<Prisma.$ReviewPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Review that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReviewFindFirstArgs} args - Arguments to find a Review
   * @example
   * // Get one Review
   * const review = await prisma.review.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends ReviewFindFirstArgs>(args?: Prisma.SelectSubset<T, ReviewFindFirstArgs<ExtArgs>>): Prisma.Prisma__ReviewClient<runtime.Types.Result.GetResult<Prisma.$ReviewPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Review that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReviewFindFirstOrThrowArgs} args - Arguments to find a Review
   * @example
   * // Get one Review
   * const review = await prisma.review.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends ReviewFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, ReviewFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__ReviewClient<runtime.Types.Result.GetResult<Prisma.$ReviewPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Reviews that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReviewFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Reviews
   * const reviews = await prisma.review.findMany()
   * 
   * // Get first 10 Reviews
   * const reviews = await prisma.review.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const reviewWithIdOnly = await prisma.review.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends ReviewFindManyArgs>(args?: Prisma.SelectSubset<T, ReviewFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ReviewPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Review.
   * @param {ReviewCreateArgs} args - Arguments to create a Review.
   * @example
   * // Create one Review
   * const Review = await prisma.review.create({
   *   data: {
   *     // ... data to create a Review
   *   }
   * })
   * 
   */
  create<T extends ReviewCreateArgs>(args: Prisma.SelectSubset<T, ReviewCreateArgs<ExtArgs>>): Prisma.Prisma__ReviewClient<runtime.Types.Result.GetResult<Prisma.$ReviewPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Reviews.
   * @param {ReviewCreateManyArgs} args - Arguments to create many Reviews.
   * @example
   * // Create many Reviews
   * const review = await prisma.review.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends ReviewCreateManyArgs>(args?: Prisma.SelectSubset<T, ReviewCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Reviews and returns the data saved in the database.
   * @param {ReviewCreateManyAndReturnArgs} args - Arguments to create many Reviews.
   * @example
   * // Create many Reviews
   * const review = await prisma.review.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Reviews and only return the `id`
   * const reviewWithIdOnly = await prisma.review.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends ReviewCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, ReviewCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ReviewPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Review.
   * @param {ReviewDeleteArgs} args - Arguments to delete one Review.
   * @example
   * // Delete one Review
   * const Review = await prisma.review.delete({
   *   where: {
   *     // ... filter to delete one Review
   *   }
   * })
   * 
   */
  delete<T extends ReviewDeleteArgs>(args: Prisma.SelectSubset<T, ReviewDeleteArgs<ExtArgs>>): Prisma.Prisma__ReviewClient<runtime.Types.Result.GetResult<Prisma.$ReviewPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Review.
   * @param {ReviewUpdateArgs} args - Arguments to update one Review.
   * @example
   * // Update one Review
   * const review = await prisma.review.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends ReviewUpdateArgs>(args: Prisma.SelectSubset<T, ReviewUpdateArgs<ExtArgs>>): Prisma.Prisma__ReviewClient<runtime.Types.Result.GetResult<Prisma.$ReviewPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Reviews.
   * @param {ReviewDeleteManyArgs} args - Arguments to filter Reviews to delete.
   * @example
   * // Delete a few Reviews
   * const { count } = await prisma.review.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends ReviewDeleteManyArgs>(args?: Prisma.SelectSubset<T, ReviewDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Reviews.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReviewUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Reviews
   * const review = await prisma.review.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends ReviewUpdateManyArgs>(args: Prisma.SelectSubset<T, ReviewUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Reviews and returns the data updated in the database.
   * @param {ReviewUpdateManyAndReturnArgs} args - Arguments to update many Reviews.
   * @example
   * // Update many Reviews
   * const review = await prisma.review.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Reviews and only return the `id`
   * const reviewWithIdOnly = await prisma.review.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends ReviewUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, ReviewUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ReviewPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Review.
   * @param {ReviewUpsertArgs} args - Arguments to update or create a Review.
   * @example
   * // Update or create a Review
   * const review = await prisma.review.upsert({
   *   create: {
   *     // ... data to create a Review
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Review we want to update
   *   }
   * })
   */
  upsert<T extends ReviewUpsertArgs>(args: Prisma.SelectSubset<T, ReviewUpsertArgs<ExtArgs>>): Prisma.Prisma__ReviewClient<runtime.Types.Result.GetResult<Prisma.$ReviewPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Reviews.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReviewCountArgs} args - Arguments to filter Reviews to count.
   * @example
   * // Count the number of Reviews
   * const count = await prisma.review.count({
   *   where: {
   *     // ... the filter for the Reviews we want to count
   *   }
   * })
  **/
  count<T extends ReviewCountArgs>(
    args?: Prisma.Subset<T, ReviewCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], ReviewCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Review.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReviewAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends ReviewAggregateArgs>(args: Prisma.Subset<T, ReviewAggregateArgs>): Prisma.PrismaPromise<GetReviewAggregateType<T>>

  /**
   * Group by Review.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReviewGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends ReviewGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: ReviewGroupByArgs['orderBy'] }
      : { orderBy?: ReviewGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, ReviewGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetReviewGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Review model
 */
readonly fields: ReviewFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Review.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__ReviewClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.UserDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.UserDefaultArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  project<T extends Prisma.ProjectDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.ProjectDefaultArgs<ExtArgs>>): Prisma.Prisma__ProjectClient<runtime.Types.Result.GetResult<Prisma.$ProjectPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  responses<T extends Prisma.Review$responsesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Review$responsesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ReviewResponsePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Review model
 */
export interface ReviewFieldRefs {
  readonly id: Prisma.FieldRef<"Review", 'String'>
  readonly userId: Prisma.FieldRef<"Review", 'String'>
  readonly projectId: Prisma.FieldRef<"Review", 'String'>
  readonly overallSentiment: Prisma.FieldRef<"Review", 'Boolean'>
  readonly overallComments: Prisma.FieldRef<"Review", 'String'>
  readonly overallCommentsRelevant: Prisma.FieldRef<"Review", 'Boolean'>
  readonly overallRelevanceScore: Prisma.FieldRef<"Review", 'Int'>
  readonly overallValidationReason: Prisma.FieldRef<"Review", 'String'>
  readonly createdAt: Prisma.FieldRef<"Review", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Review findUnique
 */
export type ReviewFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Review
   */
  select?: Prisma.ReviewSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Review
   */
  omit?: Prisma.ReviewOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReviewInclude<ExtArgs> | null
  /**
   * Filter, which Review to fetch.
   */
  where: Prisma.ReviewWhereUniqueInput
}

/**
 * Review findUniqueOrThrow
 */
export type ReviewFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Review
   */
  select?: Prisma.ReviewSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Review
   */
  omit?: Prisma.ReviewOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReviewInclude<ExtArgs> | null
  /**
   * Filter, which Review to fetch.
   */
  where: Prisma.ReviewWhereUniqueInput
}

/**
 * Review findFirst
 */
export type ReviewFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Review
   */
  select?: Prisma.ReviewSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Review
   */
  omit?: Prisma.ReviewOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReviewInclude<ExtArgs> | null
  /**
   * Filter, which Review to fetch.
   */
  where?: Prisma.ReviewWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Reviews to fetch.
   */
  orderBy?: Prisma.ReviewOrderByWithRelationInput | Prisma.ReviewOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Reviews.
   */
  cursor?: Prisma.ReviewWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Reviews from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Reviews.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Reviews.
   */
  distinct?: Prisma.ReviewScalarFieldEnum | Prisma.ReviewScalarFieldEnum[]
}

/**
 * Review findFirstOrThrow
 */
export type ReviewFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Review
   */
  select?: Prisma.ReviewSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Review
   */
  omit?: Prisma.ReviewOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReviewInclude<ExtArgs> | null
  /**
   * Filter, which Review to fetch.
   */
  where?: Prisma.ReviewWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Reviews to fetch.
   */
  orderBy?: Prisma.ReviewOrderByWithRelationInput | Prisma.ReviewOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Reviews.
   */
  cursor?: Prisma.ReviewWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Reviews from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Reviews.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Reviews.
   */
  distinct?: Prisma.ReviewScalarFieldEnum | Prisma.ReviewScalarFieldEnum[]
}

/**
 * Review findMany
 */
export type ReviewFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Review
   */
  select?: Prisma.ReviewSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Review
   */
  omit?: Prisma.ReviewOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReviewInclude<ExtArgs> | null
  /**
   * Filter, which Reviews to fetch.
   */
  where?: Prisma.ReviewWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Reviews to fetch.
   */
  orderBy?: Prisma.ReviewOrderByWithRelationInput | Prisma.ReviewOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Reviews.
   */
  cursor?: Prisma.ReviewWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Reviews from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Reviews.
   */
  skip?: number
  distinct?: Prisma.ReviewScalarFieldEnum | Prisma.ReviewScalarFieldEnum[]
}

/**
 * Review create
 */
export type ReviewCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Review
   */
  select?: Prisma.ReviewSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Review
   */
  omit?: Prisma.ReviewOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReviewInclude<ExtArgs> | null
  /**
   * The data needed to create a Review.
   */
  data: Prisma.XOR<Prisma.ReviewCreateInput, Prisma.ReviewUncheckedCreateInput>
}

/**
 * Review createMany
 */
export type ReviewCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Reviews.
   */
  data: Prisma.ReviewCreateManyInput | Prisma.ReviewCreateManyInput[]
}

/**
 * Review createManyAndReturn
 */
export type ReviewCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Review
   */
  select?: Prisma.ReviewSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Review
   */
  omit?: Prisma.ReviewOmit<ExtArgs> | null
  /**
   * The data used to create many Reviews.
   */
  data: Prisma.ReviewCreateManyInput | Prisma.ReviewCreateManyInput[]
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReviewIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Review update
 */
export type ReviewUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Review
   */
  select?: Prisma.ReviewSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Review
   */
  omit?: Prisma.ReviewOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReviewInclude<ExtArgs> | null
  /**
   * The data needed to update a Review.
   */
  data: Prisma.XOR<Prisma.ReviewUpdateInput, Prisma.ReviewUncheckedUpdateInput>
  /**
   * Choose, which Review to update.
   */
  where: Prisma.ReviewWhereUniqueInput
}

/**
 * Review updateMany
 */
export type ReviewUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Reviews.
   */
  data: Prisma.XOR<Prisma.ReviewUpdateManyMutationInput, Prisma.ReviewUncheckedUpdateManyInput>
  /**
   * Filter which Reviews to update
   */
  where?: Prisma.ReviewWhereInput
  /**
   * Limit how many Reviews to update.
   */
  limit?: number
}

/**
 * Review updateManyAndReturn
 */
export type ReviewUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Review
   */
  select?: Prisma.ReviewSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Review
   */
  omit?: Prisma.ReviewOmit<ExtArgs> | null
  /**
   * The data used to update Reviews.
   */
  data: Prisma.XOR<Prisma.ReviewUpdateManyMutationInput, Prisma.ReviewUncheckedUpdateManyInput>
  /**
   * Filter which Reviews to update
   */
  where?: Prisma.ReviewWhereInput
  /**
   * Limit how many Reviews to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReviewIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Review upsert
 */
export type ReviewUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Review
   */
  select?: Prisma.ReviewSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Review
   */
  omit?: Prisma.ReviewOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReviewInclude<ExtArgs> | null
  /**
   * The filter to search for the Review to update in case it exists.
   */
  where: Prisma.ReviewWhereUniqueInput
  /**
   * In case the Review found by the `where` argument doesn't exist, create a new Review with this data.
   */
  create: Prisma.XOR<Prisma.ReviewCreateInput, Prisma.ReviewUncheckedCreateInput>
  /**
   * In case the Review was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.ReviewUpdateInput, Prisma.ReviewUncheckedUpdateInput>
}

/**
 * Review delete
 */
export type ReviewDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Review
   */
  select?: Prisma.ReviewSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Review
   */
  omit?: Prisma.ReviewOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReviewInclude<ExtArgs> | null
  /**
   * Filter which Review to delete.
   */
  where: Prisma.ReviewWhereUniqueInput
}

/**
 * Review deleteMany
 */
export type ReviewDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Reviews to delete
   */
  where?: Prisma.ReviewWhereInput
  /**
   * Limit how many Reviews to delete.
   */
  limit?: number
}

/**
 * Review.responses
 */
export type Review$responsesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReviewResponse
   */
  select?: Prisma.ReviewResponseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReviewResponse
   */
  omit?: Prisma.ReviewResponseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReviewResponseInclude<ExtArgs> | null
  where?: Prisma.ReviewResponseWhereInput
  orderBy?: Prisma.ReviewResponseOrderByWithRelationInput | Prisma.ReviewResponseOrderByWithRelationInput[]
  cursor?: Prisma.ReviewResponseWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ReviewResponseScalarFieldEnum | Prisma.ReviewResponseScalarFieldEnum[]
}

/**
 * Review without action
 */
export type ReviewDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Review
   */
  select?: Prisma.ReviewSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Review
   */
  omit?: Prisma.ReviewOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReviewInclude<ExtArgs> | null
}
