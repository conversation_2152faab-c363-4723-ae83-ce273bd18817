
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `AIAnalysis` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model AIAnalysis
 * 
 */
export type AIAnalysisModel = runtime.Types.Result.DefaultSelection<Prisma.$AIAnalysisPayload>

export type AggregateAIAnalysis = {
  _count: AIAnalysisCountAggregateOutputType | null
  _avg: AIAnalysisAvgAggregateOutputType | null
  _sum: AIAnalysisSumAggregateOutputType | null
  _min: AIAnalysisMinAggregateOutputType | null
  _max: AIAnalysisMaxAggregateOutputType | null
}

export type AIAnalysisAvgAggregateOutputType = {
  projectFundamentalsScore: number | null
  teamGovernanceScore: number | null
  transparencyDocScore: number | null
  technologyExecutionScore: number | null
  communityCommunicationScore: number | null
  tokenUtilityTokenomicsScore: number | null
  overallScore: number | null
  confidence: number | null
}

export type AIAnalysisSumAggregateOutputType = {
  projectFundamentalsScore: number | null
  teamGovernanceScore: number | null
  transparencyDocScore: number | null
  technologyExecutionScore: number | null
  communityCommunicationScore: number | null
  tokenUtilityTokenomicsScore: number | null
  overallScore: number | null
  confidence: number | null
}

export type AIAnalysisMinAggregateOutputType = {
  id: string | null
  projectId: string | null
  projectFundamentalsScore: number | null
  teamGovernanceScore: number | null
  transparencyDocScore: number | null
  technologyExecutionScore: number | null
  communityCommunicationScore: number | null
  tokenUtilityTokenomicsScore: number | null
  overallScore: number | null
  analysis: string | null
  reasoning: string | null
  confidence: number | null
  analysisVersion: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type AIAnalysisMaxAggregateOutputType = {
  id: string | null
  projectId: string | null
  projectFundamentalsScore: number | null
  teamGovernanceScore: number | null
  transparencyDocScore: number | null
  technologyExecutionScore: number | null
  communityCommunicationScore: number | null
  tokenUtilityTokenomicsScore: number | null
  overallScore: number | null
  analysis: string | null
  reasoning: string | null
  confidence: number | null
  analysisVersion: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type AIAnalysisCountAggregateOutputType = {
  id: number
  projectId: number
  projectFundamentalsScore: number
  teamGovernanceScore: number
  transparencyDocScore: number
  technologyExecutionScore: number
  communityCommunicationScore: number
  tokenUtilityTokenomicsScore: number
  overallScore: number
  analysis: number
  reasoning: number
  strengths: number
  weaknesses: number
  recommendations: number
  confidence: number
  analysisVersion: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type AIAnalysisAvgAggregateInputType = {
  projectFundamentalsScore?: true
  teamGovernanceScore?: true
  transparencyDocScore?: true
  technologyExecutionScore?: true
  communityCommunicationScore?: true
  tokenUtilityTokenomicsScore?: true
  overallScore?: true
  confidence?: true
}

export type AIAnalysisSumAggregateInputType = {
  projectFundamentalsScore?: true
  teamGovernanceScore?: true
  transparencyDocScore?: true
  technologyExecutionScore?: true
  communityCommunicationScore?: true
  tokenUtilityTokenomicsScore?: true
  overallScore?: true
  confidence?: true
}

export type AIAnalysisMinAggregateInputType = {
  id?: true
  projectId?: true
  projectFundamentalsScore?: true
  teamGovernanceScore?: true
  transparencyDocScore?: true
  technologyExecutionScore?: true
  communityCommunicationScore?: true
  tokenUtilityTokenomicsScore?: true
  overallScore?: true
  analysis?: true
  reasoning?: true
  confidence?: true
  analysisVersion?: true
  createdAt?: true
  updatedAt?: true
}

export type AIAnalysisMaxAggregateInputType = {
  id?: true
  projectId?: true
  projectFundamentalsScore?: true
  teamGovernanceScore?: true
  transparencyDocScore?: true
  technologyExecutionScore?: true
  communityCommunicationScore?: true
  tokenUtilityTokenomicsScore?: true
  overallScore?: true
  analysis?: true
  reasoning?: true
  confidence?: true
  analysisVersion?: true
  createdAt?: true
  updatedAt?: true
}

export type AIAnalysisCountAggregateInputType = {
  id?: true
  projectId?: true
  projectFundamentalsScore?: true
  teamGovernanceScore?: true
  transparencyDocScore?: true
  technologyExecutionScore?: true
  communityCommunicationScore?: true
  tokenUtilityTokenomicsScore?: true
  overallScore?: true
  analysis?: true
  reasoning?: true
  strengths?: true
  weaknesses?: true
  recommendations?: true
  confidence?: true
  analysisVersion?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type AIAnalysisAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which AIAnalysis to aggregate.
   */
  where?: Prisma.AIAnalysisWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of AIAnalyses to fetch.
   */
  orderBy?: Prisma.AIAnalysisOrderByWithRelationInput | Prisma.AIAnalysisOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.AIAnalysisWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` AIAnalyses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` AIAnalyses.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned AIAnalyses
  **/
  _count?: true | AIAnalysisCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: AIAnalysisAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: AIAnalysisSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: AIAnalysisMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: AIAnalysisMaxAggregateInputType
}

export type GetAIAnalysisAggregateType<T extends AIAnalysisAggregateArgs> = {
      [P in keyof T & keyof AggregateAIAnalysis]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateAIAnalysis[P]>
    : Prisma.GetScalarType<T[P], AggregateAIAnalysis[P]>
}




export type AIAnalysisGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.AIAnalysisWhereInput
  orderBy?: Prisma.AIAnalysisOrderByWithAggregationInput | Prisma.AIAnalysisOrderByWithAggregationInput[]
  by: Prisma.AIAnalysisScalarFieldEnum[] | Prisma.AIAnalysisScalarFieldEnum
  having?: Prisma.AIAnalysisScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: AIAnalysisCountAggregateInputType | true
  _avg?: AIAnalysisAvgAggregateInputType
  _sum?: AIAnalysisSumAggregateInputType
  _min?: AIAnalysisMinAggregateInputType
  _max?: AIAnalysisMaxAggregateInputType
}

export type AIAnalysisGroupByOutputType = {
  id: string
  projectId: string
  projectFundamentalsScore: number
  teamGovernanceScore: number
  transparencyDocScore: number
  technologyExecutionScore: number
  communityCommunicationScore: number
  tokenUtilityTokenomicsScore: number
  overallScore: number
  analysis: string | null
  reasoning: string | null
  strengths: runtime.JsonValue | null
  weaknesses: runtime.JsonValue | null
  recommendations: runtime.JsonValue | null
  confidence: number
  analysisVersion: string
  createdAt: Date
  updatedAt: Date
  _count: AIAnalysisCountAggregateOutputType | null
  _avg: AIAnalysisAvgAggregateOutputType | null
  _sum: AIAnalysisSumAggregateOutputType | null
  _min: AIAnalysisMinAggregateOutputType | null
  _max: AIAnalysisMaxAggregateOutputType | null
}

type GetAIAnalysisGroupByPayload<T extends AIAnalysisGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<AIAnalysisGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof AIAnalysisGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], AIAnalysisGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], AIAnalysisGroupByOutputType[P]>
      }
    >
  > 



export type AIAnalysisWhereInput = {
  AND?: Prisma.AIAnalysisWhereInput | Prisma.AIAnalysisWhereInput[]
  OR?: Prisma.AIAnalysisWhereInput[]
  NOT?: Prisma.AIAnalysisWhereInput | Prisma.AIAnalysisWhereInput[]
  id?: Prisma.StringFilter<"AIAnalysis"> | string
  projectId?: Prisma.StringFilter<"AIAnalysis"> | string
  projectFundamentalsScore?: Prisma.IntFilter<"AIAnalysis"> | number
  teamGovernanceScore?: Prisma.IntFilter<"AIAnalysis"> | number
  transparencyDocScore?: Prisma.IntFilter<"AIAnalysis"> | number
  technologyExecutionScore?: Prisma.IntFilter<"AIAnalysis"> | number
  communityCommunicationScore?: Prisma.IntFilter<"AIAnalysis"> | number
  tokenUtilityTokenomicsScore?: Prisma.IntFilter<"AIAnalysis"> | number
  overallScore?: Prisma.IntFilter<"AIAnalysis"> | number
  analysis?: Prisma.StringNullableFilter<"AIAnalysis"> | string | null
  reasoning?: Prisma.StringNullableFilter<"AIAnalysis"> | string | null
  strengths?: Prisma.JsonNullableFilter<"AIAnalysis">
  weaknesses?: Prisma.JsonNullableFilter<"AIAnalysis">
  recommendations?: Prisma.JsonNullableFilter<"AIAnalysis">
  confidence?: Prisma.IntFilter<"AIAnalysis"> | number
  analysisVersion?: Prisma.StringFilter<"AIAnalysis"> | string
  createdAt?: Prisma.DateTimeFilter<"AIAnalysis"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"AIAnalysis"> | Date | string
  project?: Prisma.XOR<Prisma.ProjectScalarRelationFilter, Prisma.ProjectWhereInput>
}

export type AIAnalysisOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  projectId?: Prisma.SortOrder
  projectFundamentalsScore?: Prisma.SortOrder
  teamGovernanceScore?: Prisma.SortOrder
  transparencyDocScore?: Prisma.SortOrder
  technologyExecutionScore?: Prisma.SortOrder
  communityCommunicationScore?: Prisma.SortOrder
  tokenUtilityTokenomicsScore?: Prisma.SortOrder
  overallScore?: Prisma.SortOrder
  analysis?: Prisma.SortOrderInput | Prisma.SortOrder
  reasoning?: Prisma.SortOrderInput | Prisma.SortOrder
  strengths?: Prisma.SortOrderInput | Prisma.SortOrder
  weaknesses?: Prisma.SortOrderInput | Prisma.SortOrder
  recommendations?: Prisma.SortOrderInput | Prisma.SortOrder
  confidence?: Prisma.SortOrder
  analysisVersion?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  project?: Prisma.ProjectOrderByWithRelationInput
}

export type AIAnalysisWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  projectId?: string
  AND?: Prisma.AIAnalysisWhereInput | Prisma.AIAnalysisWhereInput[]
  OR?: Prisma.AIAnalysisWhereInput[]
  NOT?: Prisma.AIAnalysisWhereInput | Prisma.AIAnalysisWhereInput[]
  projectFundamentalsScore?: Prisma.IntFilter<"AIAnalysis"> | number
  teamGovernanceScore?: Prisma.IntFilter<"AIAnalysis"> | number
  transparencyDocScore?: Prisma.IntFilter<"AIAnalysis"> | number
  technologyExecutionScore?: Prisma.IntFilter<"AIAnalysis"> | number
  communityCommunicationScore?: Prisma.IntFilter<"AIAnalysis"> | number
  tokenUtilityTokenomicsScore?: Prisma.IntFilter<"AIAnalysis"> | number
  overallScore?: Prisma.IntFilter<"AIAnalysis"> | number
  analysis?: Prisma.StringNullableFilter<"AIAnalysis"> | string | null
  reasoning?: Prisma.StringNullableFilter<"AIAnalysis"> | string | null
  strengths?: Prisma.JsonNullableFilter<"AIAnalysis">
  weaknesses?: Prisma.JsonNullableFilter<"AIAnalysis">
  recommendations?: Prisma.JsonNullableFilter<"AIAnalysis">
  confidence?: Prisma.IntFilter<"AIAnalysis"> | number
  analysisVersion?: Prisma.StringFilter<"AIAnalysis"> | string
  createdAt?: Prisma.DateTimeFilter<"AIAnalysis"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"AIAnalysis"> | Date | string
  project?: Prisma.XOR<Prisma.ProjectScalarRelationFilter, Prisma.ProjectWhereInput>
}, "id" | "projectId">

export type AIAnalysisOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  projectId?: Prisma.SortOrder
  projectFundamentalsScore?: Prisma.SortOrder
  teamGovernanceScore?: Prisma.SortOrder
  transparencyDocScore?: Prisma.SortOrder
  technologyExecutionScore?: Prisma.SortOrder
  communityCommunicationScore?: Prisma.SortOrder
  tokenUtilityTokenomicsScore?: Prisma.SortOrder
  overallScore?: Prisma.SortOrder
  analysis?: Prisma.SortOrderInput | Prisma.SortOrder
  reasoning?: Prisma.SortOrderInput | Prisma.SortOrder
  strengths?: Prisma.SortOrderInput | Prisma.SortOrder
  weaknesses?: Prisma.SortOrderInput | Prisma.SortOrder
  recommendations?: Prisma.SortOrderInput | Prisma.SortOrder
  confidence?: Prisma.SortOrder
  analysisVersion?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.AIAnalysisCountOrderByAggregateInput
  _avg?: Prisma.AIAnalysisAvgOrderByAggregateInput
  _max?: Prisma.AIAnalysisMaxOrderByAggregateInput
  _min?: Prisma.AIAnalysisMinOrderByAggregateInput
  _sum?: Prisma.AIAnalysisSumOrderByAggregateInput
}

export type AIAnalysisScalarWhereWithAggregatesInput = {
  AND?: Prisma.AIAnalysisScalarWhereWithAggregatesInput | Prisma.AIAnalysisScalarWhereWithAggregatesInput[]
  OR?: Prisma.AIAnalysisScalarWhereWithAggregatesInput[]
  NOT?: Prisma.AIAnalysisScalarWhereWithAggregatesInput | Prisma.AIAnalysisScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"AIAnalysis"> | string
  projectId?: Prisma.StringWithAggregatesFilter<"AIAnalysis"> | string
  projectFundamentalsScore?: Prisma.IntWithAggregatesFilter<"AIAnalysis"> | number
  teamGovernanceScore?: Prisma.IntWithAggregatesFilter<"AIAnalysis"> | number
  transparencyDocScore?: Prisma.IntWithAggregatesFilter<"AIAnalysis"> | number
  technologyExecutionScore?: Prisma.IntWithAggregatesFilter<"AIAnalysis"> | number
  communityCommunicationScore?: Prisma.IntWithAggregatesFilter<"AIAnalysis"> | number
  tokenUtilityTokenomicsScore?: Prisma.IntWithAggregatesFilter<"AIAnalysis"> | number
  overallScore?: Prisma.IntWithAggregatesFilter<"AIAnalysis"> | number
  analysis?: Prisma.StringNullableWithAggregatesFilter<"AIAnalysis"> | string | null
  reasoning?: Prisma.StringNullableWithAggregatesFilter<"AIAnalysis"> | string | null
  strengths?: Prisma.JsonNullableWithAggregatesFilter<"AIAnalysis">
  weaknesses?: Prisma.JsonNullableWithAggregatesFilter<"AIAnalysis">
  recommendations?: Prisma.JsonNullableWithAggregatesFilter<"AIAnalysis">
  confidence?: Prisma.IntWithAggregatesFilter<"AIAnalysis"> | number
  analysisVersion?: Prisma.StringWithAggregatesFilter<"AIAnalysis"> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"AIAnalysis"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"AIAnalysis"> | Date | string
}

export type AIAnalysisCreateInput = {
  id?: string
  projectFundamentalsScore?: number
  teamGovernanceScore?: number
  transparencyDocScore?: number
  technologyExecutionScore?: number
  communityCommunicationScore?: number
  tokenUtilityTokenomicsScore?: number
  overallScore?: number
  analysis?: string | null
  reasoning?: string | null
  strengths?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  weaknesses?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  recommendations?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  confidence?: number
  analysisVersion?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  project: Prisma.ProjectCreateNestedOneWithoutAiAnalysisInput
}

export type AIAnalysisUncheckedCreateInput = {
  id?: string
  projectId: string
  projectFundamentalsScore?: number
  teamGovernanceScore?: number
  transparencyDocScore?: number
  technologyExecutionScore?: number
  communityCommunicationScore?: number
  tokenUtilityTokenomicsScore?: number
  overallScore?: number
  analysis?: string | null
  reasoning?: string | null
  strengths?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  weaknesses?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  recommendations?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  confidence?: number
  analysisVersion?: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type AIAnalysisUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  projectFundamentalsScore?: Prisma.IntFieldUpdateOperationsInput | number
  teamGovernanceScore?: Prisma.IntFieldUpdateOperationsInput | number
  transparencyDocScore?: Prisma.IntFieldUpdateOperationsInput | number
  technologyExecutionScore?: Prisma.IntFieldUpdateOperationsInput | number
  communityCommunicationScore?: Prisma.IntFieldUpdateOperationsInput | number
  tokenUtilityTokenomicsScore?: Prisma.IntFieldUpdateOperationsInput | number
  overallScore?: Prisma.IntFieldUpdateOperationsInput | number
  analysis?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  reasoning?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  strengths?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  weaknesses?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  recommendations?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  confidence?: Prisma.IntFieldUpdateOperationsInput | number
  analysisVersion?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  project?: Prisma.ProjectUpdateOneRequiredWithoutAiAnalysisNestedInput
}

export type AIAnalysisUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  projectId?: Prisma.StringFieldUpdateOperationsInput | string
  projectFundamentalsScore?: Prisma.IntFieldUpdateOperationsInput | number
  teamGovernanceScore?: Prisma.IntFieldUpdateOperationsInput | number
  transparencyDocScore?: Prisma.IntFieldUpdateOperationsInput | number
  technologyExecutionScore?: Prisma.IntFieldUpdateOperationsInput | number
  communityCommunicationScore?: Prisma.IntFieldUpdateOperationsInput | number
  tokenUtilityTokenomicsScore?: Prisma.IntFieldUpdateOperationsInput | number
  overallScore?: Prisma.IntFieldUpdateOperationsInput | number
  analysis?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  reasoning?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  strengths?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  weaknesses?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  recommendations?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  confidence?: Prisma.IntFieldUpdateOperationsInput | number
  analysisVersion?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type AIAnalysisCreateManyInput = {
  id?: string
  projectId: string
  projectFundamentalsScore?: number
  teamGovernanceScore?: number
  transparencyDocScore?: number
  technologyExecutionScore?: number
  communityCommunicationScore?: number
  tokenUtilityTokenomicsScore?: number
  overallScore?: number
  analysis?: string | null
  reasoning?: string | null
  strengths?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  weaknesses?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  recommendations?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  confidence?: number
  analysisVersion?: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type AIAnalysisUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  projectFundamentalsScore?: Prisma.IntFieldUpdateOperationsInput | number
  teamGovernanceScore?: Prisma.IntFieldUpdateOperationsInput | number
  transparencyDocScore?: Prisma.IntFieldUpdateOperationsInput | number
  technologyExecutionScore?: Prisma.IntFieldUpdateOperationsInput | number
  communityCommunicationScore?: Prisma.IntFieldUpdateOperationsInput | number
  tokenUtilityTokenomicsScore?: Prisma.IntFieldUpdateOperationsInput | number
  overallScore?: Prisma.IntFieldUpdateOperationsInput | number
  analysis?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  reasoning?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  strengths?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  weaknesses?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  recommendations?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  confidence?: Prisma.IntFieldUpdateOperationsInput | number
  analysisVersion?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type AIAnalysisUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  projectId?: Prisma.StringFieldUpdateOperationsInput | string
  projectFundamentalsScore?: Prisma.IntFieldUpdateOperationsInput | number
  teamGovernanceScore?: Prisma.IntFieldUpdateOperationsInput | number
  transparencyDocScore?: Prisma.IntFieldUpdateOperationsInput | number
  technologyExecutionScore?: Prisma.IntFieldUpdateOperationsInput | number
  communityCommunicationScore?: Prisma.IntFieldUpdateOperationsInput | number
  tokenUtilityTokenomicsScore?: Prisma.IntFieldUpdateOperationsInput | number
  overallScore?: Prisma.IntFieldUpdateOperationsInput | number
  analysis?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  reasoning?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  strengths?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  weaknesses?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  recommendations?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  confidence?: Prisma.IntFieldUpdateOperationsInput | number
  analysisVersion?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type AIAnalysisNullableScalarRelationFilter = {
  is?: Prisma.AIAnalysisWhereInput | null
  isNot?: Prisma.AIAnalysisWhereInput | null
}

export type AIAnalysisCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  projectId?: Prisma.SortOrder
  projectFundamentalsScore?: Prisma.SortOrder
  teamGovernanceScore?: Prisma.SortOrder
  transparencyDocScore?: Prisma.SortOrder
  technologyExecutionScore?: Prisma.SortOrder
  communityCommunicationScore?: Prisma.SortOrder
  tokenUtilityTokenomicsScore?: Prisma.SortOrder
  overallScore?: Prisma.SortOrder
  analysis?: Prisma.SortOrder
  reasoning?: Prisma.SortOrder
  strengths?: Prisma.SortOrder
  weaknesses?: Prisma.SortOrder
  recommendations?: Prisma.SortOrder
  confidence?: Prisma.SortOrder
  analysisVersion?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type AIAnalysisAvgOrderByAggregateInput = {
  projectFundamentalsScore?: Prisma.SortOrder
  teamGovernanceScore?: Prisma.SortOrder
  transparencyDocScore?: Prisma.SortOrder
  technologyExecutionScore?: Prisma.SortOrder
  communityCommunicationScore?: Prisma.SortOrder
  tokenUtilityTokenomicsScore?: Prisma.SortOrder
  overallScore?: Prisma.SortOrder
  confidence?: Prisma.SortOrder
}

export type AIAnalysisMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  projectId?: Prisma.SortOrder
  projectFundamentalsScore?: Prisma.SortOrder
  teamGovernanceScore?: Prisma.SortOrder
  transparencyDocScore?: Prisma.SortOrder
  technologyExecutionScore?: Prisma.SortOrder
  communityCommunicationScore?: Prisma.SortOrder
  tokenUtilityTokenomicsScore?: Prisma.SortOrder
  overallScore?: Prisma.SortOrder
  analysis?: Prisma.SortOrder
  reasoning?: Prisma.SortOrder
  confidence?: Prisma.SortOrder
  analysisVersion?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type AIAnalysisMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  projectId?: Prisma.SortOrder
  projectFundamentalsScore?: Prisma.SortOrder
  teamGovernanceScore?: Prisma.SortOrder
  transparencyDocScore?: Prisma.SortOrder
  technologyExecutionScore?: Prisma.SortOrder
  communityCommunicationScore?: Prisma.SortOrder
  tokenUtilityTokenomicsScore?: Prisma.SortOrder
  overallScore?: Prisma.SortOrder
  analysis?: Prisma.SortOrder
  reasoning?: Prisma.SortOrder
  confidence?: Prisma.SortOrder
  analysisVersion?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type AIAnalysisSumOrderByAggregateInput = {
  projectFundamentalsScore?: Prisma.SortOrder
  teamGovernanceScore?: Prisma.SortOrder
  transparencyDocScore?: Prisma.SortOrder
  technologyExecutionScore?: Prisma.SortOrder
  communityCommunicationScore?: Prisma.SortOrder
  tokenUtilityTokenomicsScore?: Prisma.SortOrder
  overallScore?: Prisma.SortOrder
  confidence?: Prisma.SortOrder
}

export type AIAnalysisCreateNestedOneWithoutProjectInput = {
  create?: Prisma.XOR<Prisma.AIAnalysisCreateWithoutProjectInput, Prisma.AIAnalysisUncheckedCreateWithoutProjectInput>
  connectOrCreate?: Prisma.AIAnalysisCreateOrConnectWithoutProjectInput
  connect?: Prisma.AIAnalysisWhereUniqueInput
}

export type AIAnalysisUncheckedCreateNestedOneWithoutProjectInput = {
  create?: Prisma.XOR<Prisma.AIAnalysisCreateWithoutProjectInput, Prisma.AIAnalysisUncheckedCreateWithoutProjectInput>
  connectOrCreate?: Prisma.AIAnalysisCreateOrConnectWithoutProjectInput
  connect?: Prisma.AIAnalysisWhereUniqueInput
}

export type AIAnalysisUpdateOneWithoutProjectNestedInput = {
  create?: Prisma.XOR<Prisma.AIAnalysisCreateWithoutProjectInput, Prisma.AIAnalysisUncheckedCreateWithoutProjectInput>
  connectOrCreate?: Prisma.AIAnalysisCreateOrConnectWithoutProjectInput
  upsert?: Prisma.AIAnalysisUpsertWithoutProjectInput
  disconnect?: Prisma.AIAnalysisWhereInput | boolean
  delete?: Prisma.AIAnalysisWhereInput | boolean
  connect?: Prisma.AIAnalysisWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.AIAnalysisUpdateToOneWithWhereWithoutProjectInput, Prisma.AIAnalysisUpdateWithoutProjectInput>, Prisma.AIAnalysisUncheckedUpdateWithoutProjectInput>
}

export type AIAnalysisUncheckedUpdateOneWithoutProjectNestedInput = {
  create?: Prisma.XOR<Prisma.AIAnalysisCreateWithoutProjectInput, Prisma.AIAnalysisUncheckedCreateWithoutProjectInput>
  connectOrCreate?: Prisma.AIAnalysisCreateOrConnectWithoutProjectInput
  upsert?: Prisma.AIAnalysisUpsertWithoutProjectInput
  disconnect?: Prisma.AIAnalysisWhereInput | boolean
  delete?: Prisma.AIAnalysisWhereInput | boolean
  connect?: Prisma.AIAnalysisWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.AIAnalysisUpdateToOneWithWhereWithoutProjectInput, Prisma.AIAnalysisUpdateWithoutProjectInput>, Prisma.AIAnalysisUncheckedUpdateWithoutProjectInput>
}

export type AIAnalysisCreateWithoutProjectInput = {
  id?: string
  projectFundamentalsScore?: number
  teamGovernanceScore?: number
  transparencyDocScore?: number
  technologyExecutionScore?: number
  communityCommunicationScore?: number
  tokenUtilityTokenomicsScore?: number
  overallScore?: number
  analysis?: string | null
  reasoning?: string | null
  strengths?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  weaknesses?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  recommendations?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  confidence?: number
  analysisVersion?: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type AIAnalysisUncheckedCreateWithoutProjectInput = {
  id?: string
  projectFundamentalsScore?: number
  teamGovernanceScore?: number
  transparencyDocScore?: number
  technologyExecutionScore?: number
  communityCommunicationScore?: number
  tokenUtilityTokenomicsScore?: number
  overallScore?: number
  analysis?: string | null
  reasoning?: string | null
  strengths?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  weaknesses?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  recommendations?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  confidence?: number
  analysisVersion?: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type AIAnalysisCreateOrConnectWithoutProjectInput = {
  where: Prisma.AIAnalysisWhereUniqueInput
  create: Prisma.XOR<Prisma.AIAnalysisCreateWithoutProjectInput, Prisma.AIAnalysisUncheckedCreateWithoutProjectInput>
}

export type AIAnalysisUpsertWithoutProjectInput = {
  update: Prisma.XOR<Prisma.AIAnalysisUpdateWithoutProjectInput, Prisma.AIAnalysisUncheckedUpdateWithoutProjectInput>
  create: Prisma.XOR<Prisma.AIAnalysisCreateWithoutProjectInput, Prisma.AIAnalysisUncheckedCreateWithoutProjectInput>
  where?: Prisma.AIAnalysisWhereInput
}

export type AIAnalysisUpdateToOneWithWhereWithoutProjectInput = {
  where?: Prisma.AIAnalysisWhereInput
  data: Prisma.XOR<Prisma.AIAnalysisUpdateWithoutProjectInput, Prisma.AIAnalysisUncheckedUpdateWithoutProjectInput>
}

export type AIAnalysisUpdateWithoutProjectInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  projectFundamentalsScore?: Prisma.IntFieldUpdateOperationsInput | number
  teamGovernanceScore?: Prisma.IntFieldUpdateOperationsInput | number
  transparencyDocScore?: Prisma.IntFieldUpdateOperationsInput | number
  technologyExecutionScore?: Prisma.IntFieldUpdateOperationsInput | number
  communityCommunicationScore?: Prisma.IntFieldUpdateOperationsInput | number
  tokenUtilityTokenomicsScore?: Prisma.IntFieldUpdateOperationsInput | number
  overallScore?: Prisma.IntFieldUpdateOperationsInput | number
  analysis?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  reasoning?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  strengths?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  weaknesses?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  recommendations?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  confidence?: Prisma.IntFieldUpdateOperationsInput | number
  analysisVersion?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type AIAnalysisUncheckedUpdateWithoutProjectInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  projectFundamentalsScore?: Prisma.IntFieldUpdateOperationsInput | number
  teamGovernanceScore?: Prisma.IntFieldUpdateOperationsInput | number
  transparencyDocScore?: Prisma.IntFieldUpdateOperationsInput | number
  technologyExecutionScore?: Prisma.IntFieldUpdateOperationsInput | number
  communityCommunicationScore?: Prisma.IntFieldUpdateOperationsInput | number
  tokenUtilityTokenomicsScore?: Prisma.IntFieldUpdateOperationsInput | number
  overallScore?: Prisma.IntFieldUpdateOperationsInput | number
  analysis?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  reasoning?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  strengths?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  weaknesses?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  recommendations?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  confidence?: Prisma.IntFieldUpdateOperationsInput | number
  analysisVersion?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type AIAnalysisSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  projectId?: boolean
  projectFundamentalsScore?: boolean
  teamGovernanceScore?: boolean
  transparencyDocScore?: boolean
  technologyExecutionScore?: boolean
  communityCommunicationScore?: boolean
  tokenUtilityTokenomicsScore?: boolean
  overallScore?: boolean
  analysis?: boolean
  reasoning?: boolean
  strengths?: boolean
  weaknesses?: boolean
  recommendations?: boolean
  confidence?: boolean
  analysisVersion?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  project?: boolean | Prisma.ProjectDefaultArgs<ExtArgs>
}, ExtArgs["result"]["aIAnalysis"]>

export type AIAnalysisSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  projectId?: boolean
  projectFundamentalsScore?: boolean
  teamGovernanceScore?: boolean
  transparencyDocScore?: boolean
  technologyExecutionScore?: boolean
  communityCommunicationScore?: boolean
  tokenUtilityTokenomicsScore?: boolean
  overallScore?: boolean
  analysis?: boolean
  reasoning?: boolean
  strengths?: boolean
  weaknesses?: boolean
  recommendations?: boolean
  confidence?: boolean
  analysisVersion?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  project?: boolean | Prisma.ProjectDefaultArgs<ExtArgs>
}, ExtArgs["result"]["aIAnalysis"]>

export type AIAnalysisSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  projectId?: boolean
  projectFundamentalsScore?: boolean
  teamGovernanceScore?: boolean
  transparencyDocScore?: boolean
  technologyExecutionScore?: boolean
  communityCommunicationScore?: boolean
  tokenUtilityTokenomicsScore?: boolean
  overallScore?: boolean
  analysis?: boolean
  reasoning?: boolean
  strengths?: boolean
  weaknesses?: boolean
  recommendations?: boolean
  confidence?: boolean
  analysisVersion?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  project?: boolean | Prisma.ProjectDefaultArgs<ExtArgs>
}, ExtArgs["result"]["aIAnalysis"]>

export type AIAnalysisSelectScalar = {
  id?: boolean
  projectId?: boolean
  projectFundamentalsScore?: boolean
  teamGovernanceScore?: boolean
  transparencyDocScore?: boolean
  technologyExecutionScore?: boolean
  communityCommunicationScore?: boolean
  tokenUtilityTokenomicsScore?: boolean
  overallScore?: boolean
  analysis?: boolean
  reasoning?: boolean
  strengths?: boolean
  weaknesses?: boolean
  recommendations?: boolean
  confidence?: boolean
  analysisVersion?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type AIAnalysisOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "projectId" | "projectFundamentalsScore" | "teamGovernanceScore" | "transparencyDocScore" | "technologyExecutionScore" | "communityCommunicationScore" | "tokenUtilityTokenomicsScore" | "overallScore" | "analysis" | "reasoning" | "strengths" | "weaknesses" | "recommendations" | "confidence" | "analysisVersion" | "createdAt" | "updatedAt", ExtArgs["result"]["aIAnalysis"]>
export type AIAnalysisInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  project?: boolean | Prisma.ProjectDefaultArgs<ExtArgs>
}
export type AIAnalysisIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  project?: boolean | Prisma.ProjectDefaultArgs<ExtArgs>
}
export type AIAnalysisIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  project?: boolean | Prisma.ProjectDefaultArgs<ExtArgs>
}

export type $AIAnalysisPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "AIAnalysis"
  objects: {
    project: Prisma.$ProjectPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    projectId: string
    projectFundamentalsScore: number
    teamGovernanceScore: number
    transparencyDocScore: number
    technologyExecutionScore: number
    communityCommunicationScore: number
    tokenUtilityTokenomicsScore: number
    overallScore: number
    analysis: string | null
    reasoning: string | null
    strengths: runtime.JsonValue | null
    weaknesses: runtime.JsonValue | null
    recommendations: runtime.JsonValue | null
    confidence: number
    analysisVersion: string
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["aIAnalysis"]>
  composites: {}
}

export type AIAnalysisGetPayload<S extends boolean | null | undefined | AIAnalysisDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$AIAnalysisPayload, S>

export type AIAnalysisCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<AIAnalysisFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: AIAnalysisCountAggregateInputType | true
  }

export interface AIAnalysisDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['AIAnalysis'], meta: { name: 'AIAnalysis' } }
  /**
   * Find zero or one AIAnalysis that matches the filter.
   * @param {AIAnalysisFindUniqueArgs} args - Arguments to find a AIAnalysis
   * @example
   * // Get one AIAnalysis
   * const aIAnalysis = await prisma.aIAnalysis.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends AIAnalysisFindUniqueArgs>(args: Prisma.SelectSubset<T, AIAnalysisFindUniqueArgs<ExtArgs>>): Prisma.Prisma__AIAnalysisClient<runtime.Types.Result.GetResult<Prisma.$AIAnalysisPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one AIAnalysis that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {AIAnalysisFindUniqueOrThrowArgs} args - Arguments to find a AIAnalysis
   * @example
   * // Get one AIAnalysis
   * const aIAnalysis = await prisma.aIAnalysis.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends AIAnalysisFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, AIAnalysisFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__AIAnalysisClient<runtime.Types.Result.GetResult<Prisma.$AIAnalysisPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first AIAnalysis that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AIAnalysisFindFirstArgs} args - Arguments to find a AIAnalysis
   * @example
   * // Get one AIAnalysis
   * const aIAnalysis = await prisma.aIAnalysis.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends AIAnalysisFindFirstArgs>(args?: Prisma.SelectSubset<T, AIAnalysisFindFirstArgs<ExtArgs>>): Prisma.Prisma__AIAnalysisClient<runtime.Types.Result.GetResult<Prisma.$AIAnalysisPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first AIAnalysis that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AIAnalysisFindFirstOrThrowArgs} args - Arguments to find a AIAnalysis
   * @example
   * // Get one AIAnalysis
   * const aIAnalysis = await prisma.aIAnalysis.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends AIAnalysisFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, AIAnalysisFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__AIAnalysisClient<runtime.Types.Result.GetResult<Prisma.$AIAnalysisPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more AIAnalyses that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AIAnalysisFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all AIAnalyses
   * const aIAnalyses = await prisma.aIAnalysis.findMany()
   * 
   * // Get first 10 AIAnalyses
   * const aIAnalyses = await prisma.aIAnalysis.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const aIAnalysisWithIdOnly = await prisma.aIAnalysis.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends AIAnalysisFindManyArgs>(args?: Prisma.SelectSubset<T, AIAnalysisFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$AIAnalysisPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a AIAnalysis.
   * @param {AIAnalysisCreateArgs} args - Arguments to create a AIAnalysis.
   * @example
   * // Create one AIAnalysis
   * const AIAnalysis = await prisma.aIAnalysis.create({
   *   data: {
   *     // ... data to create a AIAnalysis
   *   }
   * })
   * 
   */
  create<T extends AIAnalysisCreateArgs>(args: Prisma.SelectSubset<T, AIAnalysisCreateArgs<ExtArgs>>): Prisma.Prisma__AIAnalysisClient<runtime.Types.Result.GetResult<Prisma.$AIAnalysisPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many AIAnalyses.
   * @param {AIAnalysisCreateManyArgs} args - Arguments to create many AIAnalyses.
   * @example
   * // Create many AIAnalyses
   * const aIAnalysis = await prisma.aIAnalysis.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends AIAnalysisCreateManyArgs>(args?: Prisma.SelectSubset<T, AIAnalysisCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many AIAnalyses and returns the data saved in the database.
   * @param {AIAnalysisCreateManyAndReturnArgs} args - Arguments to create many AIAnalyses.
   * @example
   * // Create many AIAnalyses
   * const aIAnalysis = await prisma.aIAnalysis.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many AIAnalyses and only return the `id`
   * const aIAnalysisWithIdOnly = await prisma.aIAnalysis.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends AIAnalysisCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, AIAnalysisCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$AIAnalysisPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a AIAnalysis.
   * @param {AIAnalysisDeleteArgs} args - Arguments to delete one AIAnalysis.
   * @example
   * // Delete one AIAnalysis
   * const AIAnalysis = await prisma.aIAnalysis.delete({
   *   where: {
   *     // ... filter to delete one AIAnalysis
   *   }
   * })
   * 
   */
  delete<T extends AIAnalysisDeleteArgs>(args: Prisma.SelectSubset<T, AIAnalysisDeleteArgs<ExtArgs>>): Prisma.Prisma__AIAnalysisClient<runtime.Types.Result.GetResult<Prisma.$AIAnalysisPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one AIAnalysis.
   * @param {AIAnalysisUpdateArgs} args - Arguments to update one AIAnalysis.
   * @example
   * // Update one AIAnalysis
   * const aIAnalysis = await prisma.aIAnalysis.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends AIAnalysisUpdateArgs>(args: Prisma.SelectSubset<T, AIAnalysisUpdateArgs<ExtArgs>>): Prisma.Prisma__AIAnalysisClient<runtime.Types.Result.GetResult<Prisma.$AIAnalysisPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more AIAnalyses.
   * @param {AIAnalysisDeleteManyArgs} args - Arguments to filter AIAnalyses to delete.
   * @example
   * // Delete a few AIAnalyses
   * const { count } = await prisma.aIAnalysis.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends AIAnalysisDeleteManyArgs>(args?: Prisma.SelectSubset<T, AIAnalysisDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more AIAnalyses.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AIAnalysisUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many AIAnalyses
   * const aIAnalysis = await prisma.aIAnalysis.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends AIAnalysisUpdateManyArgs>(args: Prisma.SelectSubset<T, AIAnalysisUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more AIAnalyses and returns the data updated in the database.
   * @param {AIAnalysisUpdateManyAndReturnArgs} args - Arguments to update many AIAnalyses.
   * @example
   * // Update many AIAnalyses
   * const aIAnalysis = await prisma.aIAnalysis.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more AIAnalyses and only return the `id`
   * const aIAnalysisWithIdOnly = await prisma.aIAnalysis.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends AIAnalysisUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, AIAnalysisUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$AIAnalysisPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one AIAnalysis.
   * @param {AIAnalysisUpsertArgs} args - Arguments to update or create a AIAnalysis.
   * @example
   * // Update or create a AIAnalysis
   * const aIAnalysis = await prisma.aIAnalysis.upsert({
   *   create: {
   *     // ... data to create a AIAnalysis
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the AIAnalysis we want to update
   *   }
   * })
   */
  upsert<T extends AIAnalysisUpsertArgs>(args: Prisma.SelectSubset<T, AIAnalysisUpsertArgs<ExtArgs>>): Prisma.Prisma__AIAnalysisClient<runtime.Types.Result.GetResult<Prisma.$AIAnalysisPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of AIAnalyses.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AIAnalysisCountArgs} args - Arguments to filter AIAnalyses to count.
   * @example
   * // Count the number of AIAnalyses
   * const count = await prisma.aIAnalysis.count({
   *   where: {
   *     // ... the filter for the AIAnalyses we want to count
   *   }
   * })
  **/
  count<T extends AIAnalysisCountArgs>(
    args?: Prisma.Subset<T, AIAnalysisCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], AIAnalysisCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a AIAnalysis.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AIAnalysisAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends AIAnalysisAggregateArgs>(args: Prisma.Subset<T, AIAnalysisAggregateArgs>): Prisma.PrismaPromise<GetAIAnalysisAggregateType<T>>

  /**
   * Group by AIAnalysis.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AIAnalysisGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends AIAnalysisGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: AIAnalysisGroupByArgs['orderBy'] }
      : { orderBy?: AIAnalysisGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, AIAnalysisGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetAIAnalysisGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the AIAnalysis model
 */
readonly fields: AIAnalysisFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for AIAnalysis.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__AIAnalysisClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  project<T extends Prisma.ProjectDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.ProjectDefaultArgs<ExtArgs>>): Prisma.Prisma__ProjectClient<runtime.Types.Result.GetResult<Prisma.$ProjectPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the AIAnalysis model
 */
export interface AIAnalysisFieldRefs {
  readonly id: Prisma.FieldRef<"AIAnalysis", 'String'>
  readonly projectId: Prisma.FieldRef<"AIAnalysis", 'String'>
  readonly projectFundamentalsScore: Prisma.FieldRef<"AIAnalysis", 'Int'>
  readonly teamGovernanceScore: Prisma.FieldRef<"AIAnalysis", 'Int'>
  readonly transparencyDocScore: Prisma.FieldRef<"AIAnalysis", 'Int'>
  readonly technologyExecutionScore: Prisma.FieldRef<"AIAnalysis", 'Int'>
  readonly communityCommunicationScore: Prisma.FieldRef<"AIAnalysis", 'Int'>
  readonly tokenUtilityTokenomicsScore: Prisma.FieldRef<"AIAnalysis", 'Int'>
  readonly overallScore: Prisma.FieldRef<"AIAnalysis", 'Int'>
  readonly analysis: Prisma.FieldRef<"AIAnalysis", 'String'>
  readonly reasoning: Prisma.FieldRef<"AIAnalysis", 'String'>
  readonly strengths: Prisma.FieldRef<"AIAnalysis", 'Json'>
  readonly weaknesses: Prisma.FieldRef<"AIAnalysis", 'Json'>
  readonly recommendations: Prisma.FieldRef<"AIAnalysis", 'Json'>
  readonly confidence: Prisma.FieldRef<"AIAnalysis", 'Int'>
  readonly analysisVersion: Prisma.FieldRef<"AIAnalysis", 'String'>
  readonly createdAt: Prisma.FieldRef<"AIAnalysis", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"AIAnalysis", 'DateTime'>
}
    

// Custom InputTypes
/**
 * AIAnalysis findUnique
 */
export type AIAnalysisFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AIAnalysis
   */
  select?: Prisma.AIAnalysisSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AIAnalysis
   */
  omit?: Prisma.AIAnalysisOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AIAnalysisInclude<ExtArgs> | null
  /**
   * Filter, which AIAnalysis to fetch.
   */
  where: Prisma.AIAnalysisWhereUniqueInput
}

/**
 * AIAnalysis findUniqueOrThrow
 */
export type AIAnalysisFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AIAnalysis
   */
  select?: Prisma.AIAnalysisSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AIAnalysis
   */
  omit?: Prisma.AIAnalysisOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AIAnalysisInclude<ExtArgs> | null
  /**
   * Filter, which AIAnalysis to fetch.
   */
  where: Prisma.AIAnalysisWhereUniqueInput
}

/**
 * AIAnalysis findFirst
 */
export type AIAnalysisFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AIAnalysis
   */
  select?: Prisma.AIAnalysisSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AIAnalysis
   */
  omit?: Prisma.AIAnalysisOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AIAnalysisInclude<ExtArgs> | null
  /**
   * Filter, which AIAnalysis to fetch.
   */
  where?: Prisma.AIAnalysisWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of AIAnalyses to fetch.
   */
  orderBy?: Prisma.AIAnalysisOrderByWithRelationInput | Prisma.AIAnalysisOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for AIAnalyses.
   */
  cursor?: Prisma.AIAnalysisWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` AIAnalyses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` AIAnalyses.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of AIAnalyses.
   */
  distinct?: Prisma.AIAnalysisScalarFieldEnum | Prisma.AIAnalysisScalarFieldEnum[]
}

/**
 * AIAnalysis findFirstOrThrow
 */
export type AIAnalysisFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AIAnalysis
   */
  select?: Prisma.AIAnalysisSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AIAnalysis
   */
  omit?: Prisma.AIAnalysisOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AIAnalysisInclude<ExtArgs> | null
  /**
   * Filter, which AIAnalysis to fetch.
   */
  where?: Prisma.AIAnalysisWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of AIAnalyses to fetch.
   */
  orderBy?: Prisma.AIAnalysisOrderByWithRelationInput | Prisma.AIAnalysisOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for AIAnalyses.
   */
  cursor?: Prisma.AIAnalysisWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` AIAnalyses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` AIAnalyses.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of AIAnalyses.
   */
  distinct?: Prisma.AIAnalysisScalarFieldEnum | Prisma.AIAnalysisScalarFieldEnum[]
}

/**
 * AIAnalysis findMany
 */
export type AIAnalysisFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AIAnalysis
   */
  select?: Prisma.AIAnalysisSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AIAnalysis
   */
  omit?: Prisma.AIAnalysisOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AIAnalysisInclude<ExtArgs> | null
  /**
   * Filter, which AIAnalyses to fetch.
   */
  where?: Prisma.AIAnalysisWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of AIAnalyses to fetch.
   */
  orderBy?: Prisma.AIAnalysisOrderByWithRelationInput | Prisma.AIAnalysisOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing AIAnalyses.
   */
  cursor?: Prisma.AIAnalysisWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` AIAnalyses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` AIAnalyses.
   */
  skip?: number
  distinct?: Prisma.AIAnalysisScalarFieldEnum | Prisma.AIAnalysisScalarFieldEnum[]
}

/**
 * AIAnalysis create
 */
export type AIAnalysisCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AIAnalysis
   */
  select?: Prisma.AIAnalysisSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AIAnalysis
   */
  omit?: Prisma.AIAnalysisOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AIAnalysisInclude<ExtArgs> | null
  /**
   * The data needed to create a AIAnalysis.
   */
  data: Prisma.XOR<Prisma.AIAnalysisCreateInput, Prisma.AIAnalysisUncheckedCreateInput>
}

/**
 * AIAnalysis createMany
 */
export type AIAnalysisCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many AIAnalyses.
   */
  data: Prisma.AIAnalysisCreateManyInput | Prisma.AIAnalysisCreateManyInput[]
}

/**
 * AIAnalysis createManyAndReturn
 */
export type AIAnalysisCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AIAnalysis
   */
  select?: Prisma.AIAnalysisSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the AIAnalysis
   */
  omit?: Prisma.AIAnalysisOmit<ExtArgs> | null
  /**
   * The data used to create many AIAnalyses.
   */
  data: Prisma.AIAnalysisCreateManyInput | Prisma.AIAnalysisCreateManyInput[]
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AIAnalysisIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * AIAnalysis update
 */
export type AIAnalysisUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AIAnalysis
   */
  select?: Prisma.AIAnalysisSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AIAnalysis
   */
  omit?: Prisma.AIAnalysisOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AIAnalysisInclude<ExtArgs> | null
  /**
   * The data needed to update a AIAnalysis.
   */
  data: Prisma.XOR<Prisma.AIAnalysisUpdateInput, Prisma.AIAnalysisUncheckedUpdateInput>
  /**
   * Choose, which AIAnalysis to update.
   */
  where: Prisma.AIAnalysisWhereUniqueInput
}

/**
 * AIAnalysis updateMany
 */
export type AIAnalysisUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update AIAnalyses.
   */
  data: Prisma.XOR<Prisma.AIAnalysisUpdateManyMutationInput, Prisma.AIAnalysisUncheckedUpdateManyInput>
  /**
   * Filter which AIAnalyses to update
   */
  where?: Prisma.AIAnalysisWhereInput
  /**
   * Limit how many AIAnalyses to update.
   */
  limit?: number
}

/**
 * AIAnalysis updateManyAndReturn
 */
export type AIAnalysisUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AIAnalysis
   */
  select?: Prisma.AIAnalysisSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the AIAnalysis
   */
  omit?: Prisma.AIAnalysisOmit<ExtArgs> | null
  /**
   * The data used to update AIAnalyses.
   */
  data: Prisma.XOR<Prisma.AIAnalysisUpdateManyMutationInput, Prisma.AIAnalysisUncheckedUpdateManyInput>
  /**
   * Filter which AIAnalyses to update
   */
  where?: Prisma.AIAnalysisWhereInput
  /**
   * Limit how many AIAnalyses to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AIAnalysisIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * AIAnalysis upsert
 */
export type AIAnalysisUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AIAnalysis
   */
  select?: Prisma.AIAnalysisSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AIAnalysis
   */
  omit?: Prisma.AIAnalysisOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AIAnalysisInclude<ExtArgs> | null
  /**
   * The filter to search for the AIAnalysis to update in case it exists.
   */
  where: Prisma.AIAnalysisWhereUniqueInput
  /**
   * In case the AIAnalysis found by the `where` argument doesn't exist, create a new AIAnalysis with this data.
   */
  create: Prisma.XOR<Prisma.AIAnalysisCreateInput, Prisma.AIAnalysisUncheckedCreateInput>
  /**
   * In case the AIAnalysis was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.AIAnalysisUpdateInput, Prisma.AIAnalysisUncheckedUpdateInput>
}

/**
 * AIAnalysis delete
 */
export type AIAnalysisDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AIAnalysis
   */
  select?: Prisma.AIAnalysisSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AIAnalysis
   */
  omit?: Prisma.AIAnalysisOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AIAnalysisInclude<ExtArgs> | null
  /**
   * Filter which AIAnalysis to delete.
   */
  where: Prisma.AIAnalysisWhereUniqueInput
}

/**
 * AIAnalysis deleteMany
 */
export type AIAnalysisDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which AIAnalyses to delete
   */
  where?: Prisma.AIAnalysisWhereInput
  /**
   * Limit how many AIAnalyses to delete.
   */
  limit?: number
}

/**
 * AIAnalysis without action
 */
export type AIAnalysisDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AIAnalysis
   */
  select?: Prisma.AIAnalysisSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AIAnalysis
   */
  omit?: Prisma.AIAnalysisOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AIAnalysisInclude<ExtArgs> | null
}
