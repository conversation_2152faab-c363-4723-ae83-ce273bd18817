
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Project` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model Project
 * 
 */
export type ProjectModel = runtime.Types.Result.DefaultSelection<Prisma.$ProjectPayload>

export type AggregateProject = {
  _count: ProjectCountAggregateOutputType | null
  _min: ProjectMinAggregateOutputType | null
  _max: ProjectMaxAggregateOutputType | null
}

export type ProjectMinAggregateOutputType = {
  id: string | null
  title: string | null
  description: string | null
  imageUrl: string | null
  websiteUrl: string | null
  deckUrl: string | null
  whitepaperUrl: string | null
  challengeIntro: string | null
  isApprovedForVoting: boolean | null
  createdAt: Date | null
}

export type ProjectMaxAggregateOutputType = {
  id: string | null
  title: string | null
  description: string | null
  imageUrl: string | null
  websiteUrl: string | null
  deckUrl: string | null
  whitepaperUrl: string | null
  challengeIntro: string | null
  isApprovedForVoting: boolean | null
  createdAt: Date | null
}

export type ProjectCountAggregateOutputType = {
  id: number
  title: number
  description: number
  imageUrl: number
  websiteUrl: number
  deckUrl: number
  whitepaperUrl: number
  socialUrls: number
  challengeIntro: number
  isApprovedForVoting: number
  createdAt: number
  _all: number
}


export type ProjectMinAggregateInputType = {
  id?: true
  title?: true
  description?: true
  imageUrl?: true
  websiteUrl?: true
  deckUrl?: true
  whitepaperUrl?: true
  challengeIntro?: true
  isApprovedForVoting?: true
  createdAt?: true
}

export type ProjectMaxAggregateInputType = {
  id?: true
  title?: true
  description?: true
  imageUrl?: true
  websiteUrl?: true
  deckUrl?: true
  whitepaperUrl?: true
  challengeIntro?: true
  isApprovedForVoting?: true
  createdAt?: true
}

export type ProjectCountAggregateInputType = {
  id?: true
  title?: true
  description?: true
  imageUrl?: true
  websiteUrl?: true
  deckUrl?: true
  whitepaperUrl?: true
  socialUrls?: true
  challengeIntro?: true
  isApprovedForVoting?: true
  createdAt?: true
  _all?: true
}

export type ProjectAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Project to aggregate.
   */
  where?: Prisma.ProjectWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Projects to fetch.
   */
  orderBy?: Prisma.ProjectOrderByWithRelationInput | Prisma.ProjectOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.ProjectWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Projects from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Projects.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Projects
  **/
  _count?: true | ProjectCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: ProjectMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: ProjectMaxAggregateInputType
}

export type GetProjectAggregateType<T extends ProjectAggregateArgs> = {
      [P in keyof T & keyof AggregateProject]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateProject[P]>
    : Prisma.GetScalarType<T[P], AggregateProject[P]>
}




export type ProjectGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ProjectWhereInput
  orderBy?: Prisma.ProjectOrderByWithAggregationInput | Prisma.ProjectOrderByWithAggregationInput[]
  by: Prisma.ProjectScalarFieldEnum[] | Prisma.ProjectScalarFieldEnum
  having?: Prisma.ProjectScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: ProjectCountAggregateInputType | true
  _min?: ProjectMinAggregateInputType
  _max?: ProjectMaxAggregateInputType
}

export type ProjectGroupByOutputType = {
  id: string
  title: string
  description: string
  imageUrl: string | null
  websiteUrl: string | null
  deckUrl: string | null
  whitepaperUrl: string | null
  socialUrls: runtime.JsonValue | null
  challengeIntro: string
  isApprovedForVoting: boolean
  createdAt: Date
  _count: ProjectCountAggregateOutputType | null
  _min: ProjectMinAggregateOutputType | null
  _max: ProjectMaxAggregateOutputType | null
}

type GetProjectGroupByPayload<T extends ProjectGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<ProjectGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof ProjectGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], ProjectGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], ProjectGroupByOutputType[P]>
      }
    >
  > 



export type ProjectWhereInput = {
  AND?: Prisma.ProjectWhereInput | Prisma.ProjectWhereInput[]
  OR?: Prisma.ProjectWhereInput[]
  NOT?: Prisma.ProjectWhereInput | Prisma.ProjectWhereInput[]
  id?: Prisma.StringFilter<"Project"> | string
  title?: Prisma.StringFilter<"Project"> | string
  description?: Prisma.StringFilter<"Project"> | string
  imageUrl?: Prisma.StringNullableFilter<"Project"> | string | null
  websiteUrl?: Prisma.StringNullableFilter<"Project"> | string | null
  deckUrl?: Prisma.StringNullableFilter<"Project"> | string | null
  whitepaperUrl?: Prisma.StringNullableFilter<"Project"> | string | null
  socialUrls?: Prisma.JsonNullableFilter<"Project">
  challengeIntro?: Prisma.StringFilter<"Project"> | string
  isApprovedForVoting?: Prisma.BoolFilter<"Project"> | boolean
  createdAt?: Prisma.DateTimeFilter<"Project"> | Date | string
  reviews?: Prisma.ReviewListRelationFilter
  aiAnalysis?: Prisma.XOR<Prisma.AIAnalysisNullableScalarRelationFilter, Prisma.AIAnalysisWhereInput> | null
}

export type ProjectOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  description?: Prisma.SortOrder
  imageUrl?: Prisma.SortOrderInput | Prisma.SortOrder
  websiteUrl?: Prisma.SortOrderInput | Prisma.SortOrder
  deckUrl?: Prisma.SortOrderInput | Prisma.SortOrder
  whitepaperUrl?: Prisma.SortOrderInput | Prisma.SortOrder
  socialUrls?: Prisma.SortOrderInput | Prisma.SortOrder
  challengeIntro?: Prisma.SortOrder
  isApprovedForVoting?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  reviews?: Prisma.ReviewOrderByRelationAggregateInput
  aiAnalysis?: Prisma.AIAnalysisOrderByWithRelationInput
}

export type ProjectWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.ProjectWhereInput | Prisma.ProjectWhereInput[]
  OR?: Prisma.ProjectWhereInput[]
  NOT?: Prisma.ProjectWhereInput | Prisma.ProjectWhereInput[]
  title?: Prisma.StringFilter<"Project"> | string
  description?: Prisma.StringFilter<"Project"> | string
  imageUrl?: Prisma.StringNullableFilter<"Project"> | string | null
  websiteUrl?: Prisma.StringNullableFilter<"Project"> | string | null
  deckUrl?: Prisma.StringNullableFilter<"Project"> | string | null
  whitepaperUrl?: Prisma.StringNullableFilter<"Project"> | string | null
  socialUrls?: Prisma.JsonNullableFilter<"Project">
  challengeIntro?: Prisma.StringFilter<"Project"> | string
  isApprovedForVoting?: Prisma.BoolFilter<"Project"> | boolean
  createdAt?: Prisma.DateTimeFilter<"Project"> | Date | string
  reviews?: Prisma.ReviewListRelationFilter
  aiAnalysis?: Prisma.XOR<Prisma.AIAnalysisNullableScalarRelationFilter, Prisma.AIAnalysisWhereInput> | null
}, "id">

export type ProjectOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  description?: Prisma.SortOrder
  imageUrl?: Prisma.SortOrderInput | Prisma.SortOrder
  websiteUrl?: Prisma.SortOrderInput | Prisma.SortOrder
  deckUrl?: Prisma.SortOrderInput | Prisma.SortOrder
  whitepaperUrl?: Prisma.SortOrderInput | Prisma.SortOrder
  socialUrls?: Prisma.SortOrderInput | Prisma.SortOrder
  challengeIntro?: Prisma.SortOrder
  isApprovedForVoting?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  _count?: Prisma.ProjectCountOrderByAggregateInput
  _max?: Prisma.ProjectMaxOrderByAggregateInput
  _min?: Prisma.ProjectMinOrderByAggregateInput
}

export type ProjectScalarWhereWithAggregatesInput = {
  AND?: Prisma.ProjectScalarWhereWithAggregatesInput | Prisma.ProjectScalarWhereWithAggregatesInput[]
  OR?: Prisma.ProjectScalarWhereWithAggregatesInput[]
  NOT?: Prisma.ProjectScalarWhereWithAggregatesInput | Prisma.ProjectScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Project"> | string
  title?: Prisma.StringWithAggregatesFilter<"Project"> | string
  description?: Prisma.StringWithAggregatesFilter<"Project"> | string
  imageUrl?: Prisma.StringNullableWithAggregatesFilter<"Project"> | string | null
  websiteUrl?: Prisma.StringNullableWithAggregatesFilter<"Project"> | string | null
  deckUrl?: Prisma.StringNullableWithAggregatesFilter<"Project"> | string | null
  whitepaperUrl?: Prisma.StringNullableWithAggregatesFilter<"Project"> | string | null
  socialUrls?: Prisma.JsonNullableWithAggregatesFilter<"Project">
  challengeIntro?: Prisma.StringWithAggregatesFilter<"Project"> | string
  isApprovedForVoting?: Prisma.BoolWithAggregatesFilter<"Project"> | boolean
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Project"> | Date | string
}

export type ProjectCreateInput = {
  id?: string
  title: string
  description: string
  imageUrl?: string | null
  websiteUrl?: string | null
  deckUrl?: string | null
  whitepaperUrl?: string | null
  socialUrls?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  challengeIntro: string
  isApprovedForVoting?: boolean
  createdAt?: Date | string
  reviews?: Prisma.ReviewCreateNestedManyWithoutProjectInput
  aiAnalysis?: Prisma.AIAnalysisCreateNestedOneWithoutProjectInput
}

export type ProjectUncheckedCreateInput = {
  id?: string
  title: string
  description: string
  imageUrl?: string | null
  websiteUrl?: string | null
  deckUrl?: string | null
  whitepaperUrl?: string | null
  socialUrls?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  challengeIntro: string
  isApprovedForVoting?: boolean
  createdAt?: Date | string
  reviews?: Prisma.ReviewUncheckedCreateNestedManyWithoutProjectInput
  aiAnalysis?: Prisma.AIAnalysisUncheckedCreateNestedOneWithoutProjectInput
}

export type ProjectUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  websiteUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deckUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  whitepaperUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  socialUrls?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  challengeIntro?: Prisma.StringFieldUpdateOperationsInput | string
  isApprovedForVoting?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  reviews?: Prisma.ReviewUpdateManyWithoutProjectNestedInput
  aiAnalysis?: Prisma.AIAnalysisUpdateOneWithoutProjectNestedInput
}

export type ProjectUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  websiteUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deckUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  whitepaperUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  socialUrls?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  challengeIntro?: Prisma.StringFieldUpdateOperationsInput | string
  isApprovedForVoting?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  reviews?: Prisma.ReviewUncheckedUpdateManyWithoutProjectNestedInput
  aiAnalysis?: Prisma.AIAnalysisUncheckedUpdateOneWithoutProjectNestedInput
}

export type ProjectCreateManyInput = {
  id?: string
  title: string
  description: string
  imageUrl?: string | null
  websiteUrl?: string | null
  deckUrl?: string | null
  whitepaperUrl?: string | null
  socialUrls?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  challengeIntro: string
  isApprovedForVoting?: boolean
  createdAt?: Date | string
}

export type ProjectUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  websiteUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deckUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  whitepaperUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  socialUrls?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  challengeIntro?: Prisma.StringFieldUpdateOperationsInput | string
  isApprovedForVoting?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ProjectUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  websiteUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deckUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  whitepaperUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  socialUrls?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  challengeIntro?: Prisma.StringFieldUpdateOperationsInput | string
  isApprovedForVoting?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ProjectCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  description?: Prisma.SortOrder
  imageUrl?: Prisma.SortOrder
  websiteUrl?: Prisma.SortOrder
  deckUrl?: Prisma.SortOrder
  whitepaperUrl?: Prisma.SortOrder
  socialUrls?: Prisma.SortOrder
  challengeIntro?: Prisma.SortOrder
  isApprovedForVoting?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type ProjectMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  description?: Prisma.SortOrder
  imageUrl?: Prisma.SortOrder
  websiteUrl?: Prisma.SortOrder
  deckUrl?: Prisma.SortOrder
  whitepaperUrl?: Prisma.SortOrder
  challengeIntro?: Prisma.SortOrder
  isApprovedForVoting?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type ProjectMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  description?: Prisma.SortOrder
  imageUrl?: Prisma.SortOrder
  websiteUrl?: Prisma.SortOrder
  deckUrl?: Prisma.SortOrder
  whitepaperUrl?: Prisma.SortOrder
  challengeIntro?: Prisma.SortOrder
  isApprovedForVoting?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type ProjectScalarRelationFilter = {
  is?: Prisma.ProjectWhereInput
  isNot?: Prisma.ProjectWhereInput
}

export type NullableStringFieldUpdateOperationsInput = {
  set?: string | null
}

export type ProjectCreateNestedOneWithoutReviewsInput = {
  create?: Prisma.XOR<Prisma.ProjectCreateWithoutReviewsInput, Prisma.ProjectUncheckedCreateWithoutReviewsInput>
  connectOrCreate?: Prisma.ProjectCreateOrConnectWithoutReviewsInput
  connect?: Prisma.ProjectWhereUniqueInput
}

export type ProjectUpdateOneRequiredWithoutReviewsNestedInput = {
  create?: Prisma.XOR<Prisma.ProjectCreateWithoutReviewsInput, Prisma.ProjectUncheckedCreateWithoutReviewsInput>
  connectOrCreate?: Prisma.ProjectCreateOrConnectWithoutReviewsInput
  upsert?: Prisma.ProjectUpsertWithoutReviewsInput
  connect?: Prisma.ProjectWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.ProjectUpdateToOneWithWhereWithoutReviewsInput, Prisma.ProjectUpdateWithoutReviewsInput>, Prisma.ProjectUncheckedUpdateWithoutReviewsInput>
}

export type ProjectCreateNestedOneWithoutAiAnalysisInput = {
  create?: Prisma.XOR<Prisma.ProjectCreateWithoutAiAnalysisInput, Prisma.ProjectUncheckedCreateWithoutAiAnalysisInput>
  connectOrCreate?: Prisma.ProjectCreateOrConnectWithoutAiAnalysisInput
  connect?: Prisma.ProjectWhereUniqueInput
}

export type ProjectUpdateOneRequiredWithoutAiAnalysisNestedInput = {
  create?: Prisma.XOR<Prisma.ProjectCreateWithoutAiAnalysisInput, Prisma.ProjectUncheckedCreateWithoutAiAnalysisInput>
  connectOrCreate?: Prisma.ProjectCreateOrConnectWithoutAiAnalysisInput
  upsert?: Prisma.ProjectUpsertWithoutAiAnalysisInput
  connect?: Prisma.ProjectWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.ProjectUpdateToOneWithWhereWithoutAiAnalysisInput, Prisma.ProjectUpdateWithoutAiAnalysisInput>, Prisma.ProjectUncheckedUpdateWithoutAiAnalysisInput>
}

export type ProjectCreateWithoutReviewsInput = {
  id?: string
  title: string
  description: string
  imageUrl?: string | null
  websiteUrl?: string | null
  deckUrl?: string | null
  whitepaperUrl?: string | null
  socialUrls?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  challengeIntro: string
  isApprovedForVoting?: boolean
  createdAt?: Date | string
  aiAnalysis?: Prisma.AIAnalysisCreateNestedOneWithoutProjectInput
}

export type ProjectUncheckedCreateWithoutReviewsInput = {
  id?: string
  title: string
  description: string
  imageUrl?: string | null
  websiteUrl?: string | null
  deckUrl?: string | null
  whitepaperUrl?: string | null
  socialUrls?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  challengeIntro: string
  isApprovedForVoting?: boolean
  createdAt?: Date | string
  aiAnalysis?: Prisma.AIAnalysisUncheckedCreateNestedOneWithoutProjectInput
}

export type ProjectCreateOrConnectWithoutReviewsInput = {
  where: Prisma.ProjectWhereUniqueInput
  create: Prisma.XOR<Prisma.ProjectCreateWithoutReviewsInput, Prisma.ProjectUncheckedCreateWithoutReviewsInput>
}

export type ProjectUpsertWithoutReviewsInput = {
  update: Prisma.XOR<Prisma.ProjectUpdateWithoutReviewsInput, Prisma.ProjectUncheckedUpdateWithoutReviewsInput>
  create: Prisma.XOR<Prisma.ProjectCreateWithoutReviewsInput, Prisma.ProjectUncheckedCreateWithoutReviewsInput>
  where?: Prisma.ProjectWhereInput
}

export type ProjectUpdateToOneWithWhereWithoutReviewsInput = {
  where?: Prisma.ProjectWhereInput
  data: Prisma.XOR<Prisma.ProjectUpdateWithoutReviewsInput, Prisma.ProjectUncheckedUpdateWithoutReviewsInput>
}

export type ProjectUpdateWithoutReviewsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  websiteUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deckUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  whitepaperUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  socialUrls?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  challengeIntro?: Prisma.StringFieldUpdateOperationsInput | string
  isApprovedForVoting?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  aiAnalysis?: Prisma.AIAnalysisUpdateOneWithoutProjectNestedInput
}

export type ProjectUncheckedUpdateWithoutReviewsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  websiteUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deckUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  whitepaperUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  socialUrls?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  challengeIntro?: Prisma.StringFieldUpdateOperationsInput | string
  isApprovedForVoting?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  aiAnalysis?: Prisma.AIAnalysisUncheckedUpdateOneWithoutProjectNestedInput
}

export type ProjectCreateWithoutAiAnalysisInput = {
  id?: string
  title: string
  description: string
  imageUrl?: string | null
  websiteUrl?: string | null
  deckUrl?: string | null
  whitepaperUrl?: string | null
  socialUrls?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  challengeIntro: string
  isApprovedForVoting?: boolean
  createdAt?: Date | string
  reviews?: Prisma.ReviewCreateNestedManyWithoutProjectInput
}

export type ProjectUncheckedCreateWithoutAiAnalysisInput = {
  id?: string
  title: string
  description: string
  imageUrl?: string | null
  websiteUrl?: string | null
  deckUrl?: string | null
  whitepaperUrl?: string | null
  socialUrls?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  challengeIntro: string
  isApprovedForVoting?: boolean
  createdAt?: Date | string
  reviews?: Prisma.ReviewUncheckedCreateNestedManyWithoutProjectInput
}

export type ProjectCreateOrConnectWithoutAiAnalysisInput = {
  where: Prisma.ProjectWhereUniqueInput
  create: Prisma.XOR<Prisma.ProjectCreateWithoutAiAnalysisInput, Prisma.ProjectUncheckedCreateWithoutAiAnalysisInput>
}

export type ProjectUpsertWithoutAiAnalysisInput = {
  update: Prisma.XOR<Prisma.ProjectUpdateWithoutAiAnalysisInput, Prisma.ProjectUncheckedUpdateWithoutAiAnalysisInput>
  create: Prisma.XOR<Prisma.ProjectCreateWithoutAiAnalysisInput, Prisma.ProjectUncheckedCreateWithoutAiAnalysisInput>
  where?: Prisma.ProjectWhereInput
}

export type ProjectUpdateToOneWithWhereWithoutAiAnalysisInput = {
  where?: Prisma.ProjectWhereInput
  data: Prisma.XOR<Prisma.ProjectUpdateWithoutAiAnalysisInput, Prisma.ProjectUncheckedUpdateWithoutAiAnalysisInput>
}

export type ProjectUpdateWithoutAiAnalysisInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  websiteUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deckUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  whitepaperUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  socialUrls?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  challengeIntro?: Prisma.StringFieldUpdateOperationsInput | string
  isApprovedForVoting?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  reviews?: Prisma.ReviewUpdateManyWithoutProjectNestedInput
}

export type ProjectUncheckedUpdateWithoutAiAnalysisInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  websiteUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deckUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  whitepaperUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  socialUrls?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  challengeIntro?: Prisma.StringFieldUpdateOperationsInput | string
  isApprovedForVoting?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  reviews?: Prisma.ReviewUncheckedUpdateManyWithoutProjectNestedInput
}


/**
 * Count Type ProjectCountOutputType
 */

export type ProjectCountOutputType = {
  reviews: number
}

export type ProjectCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  reviews?: boolean | ProjectCountOutputTypeCountReviewsArgs
}

/**
 * ProjectCountOutputType without action
 */
export type ProjectCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ProjectCountOutputType
   */
  select?: Prisma.ProjectCountOutputTypeSelect<ExtArgs> | null
}

/**
 * ProjectCountOutputType without action
 */
export type ProjectCountOutputTypeCountReviewsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ReviewWhereInput
}


export type ProjectSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  title?: boolean
  description?: boolean
  imageUrl?: boolean
  websiteUrl?: boolean
  deckUrl?: boolean
  whitepaperUrl?: boolean
  socialUrls?: boolean
  challengeIntro?: boolean
  isApprovedForVoting?: boolean
  createdAt?: boolean
  reviews?: boolean | Prisma.Project$reviewsArgs<ExtArgs>
  aiAnalysis?: boolean | Prisma.Project$aiAnalysisArgs<ExtArgs>
  _count?: boolean | Prisma.ProjectCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["project"]>

export type ProjectSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  title?: boolean
  description?: boolean
  imageUrl?: boolean
  websiteUrl?: boolean
  deckUrl?: boolean
  whitepaperUrl?: boolean
  socialUrls?: boolean
  challengeIntro?: boolean
  isApprovedForVoting?: boolean
  createdAt?: boolean
}, ExtArgs["result"]["project"]>

export type ProjectSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  title?: boolean
  description?: boolean
  imageUrl?: boolean
  websiteUrl?: boolean
  deckUrl?: boolean
  whitepaperUrl?: boolean
  socialUrls?: boolean
  challengeIntro?: boolean
  isApprovedForVoting?: boolean
  createdAt?: boolean
}, ExtArgs["result"]["project"]>

export type ProjectSelectScalar = {
  id?: boolean
  title?: boolean
  description?: boolean
  imageUrl?: boolean
  websiteUrl?: boolean
  deckUrl?: boolean
  whitepaperUrl?: boolean
  socialUrls?: boolean
  challengeIntro?: boolean
  isApprovedForVoting?: boolean
  createdAt?: boolean
}

export type ProjectOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "title" | "description" | "imageUrl" | "websiteUrl" | "deckUrl" | "whitepaperUrl" | "socialUrls" | "challengeIntro" | "isApprovedForVoting" | "createdAt", ExtArgs["result"]["project"]>
export type ProjectInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  reviews?: boolean | Prisma.Project$reviewsArgs<ExtArgs>
  aiAnalysis?: boolean | Prisma.Project$aiAnalysisArgs<ExtArgs>
  _count?: boolean | Prisma.ProjectCountOutputTypeDefaultArgs<ExtArgs>
}
export type ProjectIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {}
export type ProjectIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {}

export type $ProjectPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Project"
  objects: {
    reviews: Prisma.$ReviewPayload<ExtArgs>[]
    aiAnalysis: Prisma.$AIAnalysisPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    title: string
    description: string
    imageUrl: string | null
    websiteUrl: string | null
    deckUrl: string | null
    whitepaperUrl: string | null
    socialUrls: runtime.JsonValue | null
    challengeIntro: string
    isApprovedForVoting: boolean
    createdAt: Date
  }, ExtArgs["result"]["project"]>
  composites: {}
}

export type ProjectGetPayload<S extends boolean | null | undefined | ProjectDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$ProjectPayload, S>

export type ProjectCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<ProjectFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: ProjectCountAggregateInputType | true
  }

export interface ProjectDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Project'], meta: { name: 'Project' } }
  /**
   * Find zero or one Project that matches the filter.
   * @param {ProjectFindUniqueArgs} args - Arguments to find a Project
   * @example
   * // Get one Project
   * const project = await prisma.project.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends ProjectFindUniqueArgs>(args: Prisma.SelectSubset<T, ProjectFindUniqueArgs<ExtArgs>>): Prisma.Prisma__ProjectClient<runtime.Types.Result.GetResult<Prisma.$ProjectPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Project that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {ProjectFindUniqueOrThrowArgs} args - Arguments to find a Project
   * @example
   * // Get one Project
   * const project = await prisma.project.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends ProjectFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, ProjectFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__ProjectClient<runtime.Types.Result.GetResult<Prisma.$ProjectPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Project that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProjectFindFirstArgs} args - Arguments to find a Project
   * @example
   * // Get one Project
   * const project = await prisma.project.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends ProjectFindFirstArgs>(args?: Prisma.SelectSubset<T, ProjectFindFirstArgs<ExtArgs>>): Prisma.Prisma__ProjectClient<runtime.Types.Result.GetResult<Prisma.$ProjectPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Project that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProjectFindFirstOrThrowArgs} args - Arguments to find a Project
   * @example
   * // Get one Project
   * const project = await prisma.project.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends ProjectFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, ProjectFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__ProjectClient<runtime.Types.Result.GetResult<Prisma.$ProjectPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Projects that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProjectFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Projects
   * const projects = await prisma.project.findMany()
   * 
   * // Get first 10 Projects
   * const projects = await prisma.project.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const projectWithIdOnly = await prisma.project.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends ProjectFindManyArgs>(args?: Prisma.SelectSubset<T, ProjectFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ProjectPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Project.
   * @param {ProjectCreateArgs} args - Arguments to create a Project.
   * @example
   * // Create one Project
   * const Project = await prisma.project.create({
   *   data: {
   *     // ... data to create a Project
   *   }
   * })
   * 
   */
  create<T extends ProjectCreateArgs>(args: Prisma.SelectSubset<T, ProjectCreateArgs<ExtArgs>>): Prisma.Prisma__ProjectClient<runtime.Types.Result.GetResult<Prisma.$ProjectPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Projects.
   * @param {ProjectCreateManyArgs} args - Arguments to create many Projects.
   * @example
   * // Create many Projects
   * const project = await prisma.project.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends ProjectCreateManyArgs>(args?: Prisma.SelectSubset<T, ProjectCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Projects and returns the data saved in the database.
   * @param {ProjectCreateManyAndReturnArgs} args - Arguments to create many Projects.
   * @example
   * // Create many Projects
   * const project = await prisma.project.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Projects and only return the `id`
   * const projectWithIdOnly = await prisma.project.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends ProjectCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, ProjectCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ProjectPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Project.
   * @param {ProjectDeleteArgs} args - Arguments to delete one Project.
   * @example
   * // Delete one Project
   * const Project = await prisma.project.delete({
   *   where: {
   *     // ... filter to delete one Project
   *   }
   * })
   * 
   */
  delete<T extends ProjectDeleteArgs>(args: Prisma.SelectSubset<T, ProjectDeleteArgs<ExtArgs>>): Prisma.Prisma__ProjectClient<runtime.Types.Result.GetResult<Prisma.$ProjectPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Project.
   * @param {ProjectUpdateArgs} args - Arguments to update one Project.
   * @example
   * // Update one Project
   * const project = await prisma.project.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends ProjectUpdateArgs>(args: Prisma.SelectSubset<T, ProjectUpdateArgs<ExtArgs>>): Prisma.Prisma__ProjectClient<runtime.Types.Result.GetResult<Prisma.$ProjectPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Projects.
   * @param {ProjectDeleteManyArgs} args - Arguments to filter Projects to delete.
   * @example
   * // Delete a few Projects
   * const { count } = await prisma.project.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends ProjectDeleteManyArgs>(args?: Prisma.SelectSubset<T, ProjectDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Projects.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProjectUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Projects
   * const project = await prisma.project.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends ProjectUpdateManyArgs>(args: Prisma.SelectSubset<T, ProjectUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Projects and returns the data updated in the database.
   * @param {ProjectUpdateManyAndReturnArgs} args - Arguments to update many Projects.
   * @example
   * // Update many Projects
   * const project = await prisma.project.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Projects and only return the `id`
   * const projectWithIdOnly = await prisma.project.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends ProjectUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, ProjectUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ProjectPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Project.
   * @param {ProjectUpsertArgs} args - Arguments to update or create a Project.
   * @example
   * // Update or create a Project
   * const project = await prisma.project.upsert({
   *   create: {
   *     // ... data to create a Project
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Project we want to update
   *   }
   * })
   */
  upsert<T extends ProjectUpsertArgs>(args: Prisma.SelectSubset<T, ProjectUpsertArgs<ExtArgs>>): Prisma.Prisma__ProjectClient<runtime.Types.Result.GetResult<Prisma.$ProjectPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Projects.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProjectCountArgs} args - Arguments to filter Projects to count.
   * @example
   * // Count the number of Projects
   * const count = await prisma.project.count({
   *   where: {
   *     // ... the filter for the Projects we want to count
   *   }
   * })
  **/
  count<T extends ProjectCountArgs>(
    args?: Prisma.Subset<T, ProjectCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], ProjectCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Project.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProjectAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends ProjectAggregateArgs>(args: Prisma.Subset<T, ProjectAggregateArgs>): Prisma.PrismaPromise<GetProjectAggregateType<T>>

  /**
   * Group by Project.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProjectGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends ProjectGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: ProjectGroupByArgs['orderBy'] }
      : { orderBy?: ProjectGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, ProjectGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetProjectGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Project model
 */
readonly fields: ProjectFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Project.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__ProjectClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  reviews<T extends Prisma.Project$reviewsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Project$reviewsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ReviewPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  aiAnalysis<T extends Prisma.Project$aiAnalysisArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Project$aiAnalysisArgs<ExtArgs>>): Prisma.Prisma__AIAnalysisClient<runtime.Types.Result.GetResult<Prisma.$AIAnalysisPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Project model
 */
export interface ProjectFieldRefs {
  readonly id: Prisma.FieldRef<"Project", 'String'>
  readonly title: Prisma.FieldRef<"Project", 'String'>
  readonly description: Prisma.FieldRef<"Project", 'String'>
  readonly imageUrl: Prisma.FieldRef<"Project", 'String'>
  readonly websiteUrl: Prisma.FieldRef<"Project", 'String'>
  readonly deckUrl: Prisma.FieldRef<"Project", 'String'>
  readonly whitepaperUrl: Prisma.FieldRef<"Project", 'String'>
  readonly socialUrls: Prisma.FieldRef<"Project", 'Json'>
  readonly challengeIntro: Prisma.FieldRef<"Project", 'String'>
  readonly isApprovedForVoting: Prisma.FieldRef<"Project", 'Boolean'>
  readonly createdAt: Prisma.FieldRef<"Project", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Project findUnique
 */
export type ProjectFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Project
   */
  select?: Prisma.ProjectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Project
   */
  omit?: Prisma.ProjectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProjectInclude<ExtArgs> | null
  /**
   * Filter, which Project to fetch.
   */
  where: Prisma.ProjectWhereUniqueInput
}

/**
 * Project findUniqueOrThrow
 */
export type ProjectFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Project
   */
  select?: Prisma.ProjectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Project
   */
  omit?: Prisma.ProjectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProjectInclude<ExtArgs> | null
  /**
   * Filter, which Project to fetch.
   */
  where: Prisma.ProjectWhereUniqueInput
}

/**
 * Project findFirst
 */
export type ProjectFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Project
   */
  select?: Prisma.ProjectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Project
   */
  omit?: Prisma.ProjectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProjectInclude<ExtArgs> | null
  /**
   * Filter, which Project to fetch.
   */
  where?: Prisma.ProjectWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Projects to fetch.
   */
  orderBy?: Prisma.ProjectOrderByWithRelationInput | Prisma.ProjectOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Projects.
   */
  cursor?: Prisma.ProjectWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Projects from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Projects.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Projects.
   */
  distinct?: Prisma.ProjectScalarFieldEnum | Prisma.ProjectScalarFieldEnum[]
}

/**
 * Project findFirstOrThrow
 */
export type ProjectFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Project
   */
  select?: Prisma.ProjectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Project
   */
  omit?: Prisma.ProjectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProjectInclude<ExtArgs> | null
  /**
   * Filter, which Project to fetch.
   */
  where?: Prisma.ProjectWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Projects to fetch.
   */
  orderBy?: Prisma.ProjectOrderByWithRelationInput | Prisma.ProjectOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Projects.
   */
  cursor?: Prisma.ProjectWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Projects from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Projects.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Projects.
   */
  distinct?: Prisma.ProjectScalarFieldEnum | Prisma.ProjectScalarFieldEnum[]
}

/**
 * Project findMany
 */
export type ProjectFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Project
   */
  select?: Prisma.ProjectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Project
   */
  omit?: Prisma.ProjectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProjectInclude<ExtArgs> | null
  /**
   * Filter, which Projects to fetch.
   */
  where?: Prisma.ProjectWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Projects to fetch.
   */
  orderBy?: Prisma.ProjectOrderByWithRelationInput | Prisma.ProjectOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Projects.
   */
  cursor?: Prisma.ProjectWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Projects from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Projects.
   */
  skip?: number
  distinct?: Prisma.ProjectScalarFieldEnum | Prisma.ProjectScalarFieldEnum[]
}

/**
 * Project create
 */
export type ProjectCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Project
   */
  select?: Prisma.ProjectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Project
   */
  omit?: Prisma.ProjectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProjectInclude<ExtArgs> | null
  /**
   * The data needed to create a Project.
   */
  data: Prisma.XOR<Prisma.ProjectCreateInput, Prisma.ProjectUncheckedCreateInput>
}

/**
 * Project createMany
 */
export type ProjectCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Projects.
   */
  data: Prisma.ProjectCreateManyInput | Prisma.ProjectCreateManyInput[]
}

/**
 * Project createManyAndReturn
 */
export type ProjectCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Project
   */
  select?: Prisma.ProjectSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Project
   */
  omit?: Prisma.ProjectOmit<ExtArgs> | null
  /**
   * The data used to create many Projects.
   */
  data: Prisma.ProjectCreateManyInput | Prisma.ProjectCreateManyInput[]
}

/**
 * Project update
 */
export type ProjectUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Project
   */
  select?: Prisma.ProjectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Project
   */
  omit?: Prisma.ProjectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProjectInclude<ExtArgs> | null
  /**
   * The data needed to update a Project.
   */
  data: Prisma.XOR<Prisma.ProjectUpdateInput, Prisma.ProjectUncheckedUpdateInput>
  /**
   * Choose, which Project to update.
   */
  where: Prisma.ProjectWhereUniqueInput
}

/**
 * Project updateMany
 */
export type ProjectUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Projects.
   */
  data: Prisma.XOR<Prisma.ProjectUpdateManyMutationInput, Prisma.ProjectUncheckedUpdateManyInput>
  /**
   * Filter which Projects to update
   */
  where?: Prisma.ProjectWhereInput
  /**
   * Limit how many Projects to update.
   */
  limit?: number
}

/**
 * Project updateManyAndReturn
 */
export type ProjectUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Project
   */
  select?: Prisma.ProjectSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Project
   */
  omit?: Prisma.ProjectOmit<ExtArgs> | null
  /**
   * The data used to update Projects.
   */
  data: Prisma.XOR<Prisma.ProjectUpdateManyMutationInput, Prisma.ProjectUncheckedUpdateManyInput>
  /**
   * Filter which Projects to update
   */
  where?: Prisma.ProjectWhereInput
  /**
   * Limit how many Projects to update.
   */
  limit?: number
}

/**
 * Project upsert
 */
export type ProjectUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Project
   */
  select?: Prisma.ProjectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Project
   */
  omit?: Prisma.ProjectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProjectInclude<ExtArgs> | null
  /**
   * The filter to search for the Project to update in case it exists.
   */
  where: Prisma.ProjectWhereUniqueInput
  /**
   * In case the Project found by the `where` argument doesn't exist, create a new Project with this data.
   */
  create: Prisma.XOR<Prisma.ProjectCreateInput, Prisma.ProjectUncheckedCreateInput>
  /**
   * In case the Project was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.ProjectUpdateInput, Prisma.ProjectUncheckedUpdateInput>
}

/**
 * Project delete
 */
export type ProjectDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Project
   */
  select?: Prisma.ProjectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Project
   */
  omit?: Prisma.ProjectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProjectInclude<ExtArgs> | null
  /**
   * Filter which Project to delete.
   */
  where: Prisma.ProjectWhereUniqueInput
}

/**
 * Project deleteMany
 */
export type ProjectDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Projects to delete
   */
  where?: Prisma.ProjectWhereInput
  /**
   * Limit how many Projects to delete.
   */
  limit?: number
}

/**
 * Project.reviews
 */
export type Project$reviewsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Review
   */
  select?: Prisma.ReviewSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Review
   */
  omit?: Prisma.ReviewOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReviewInclude<ExtArgs> | null
  where?: Prisma.ReviewWhereInput
  orderBy?: Prisma.ReviewOrderByWithRelationInput | Prisma.ReviewOrderByWithRelationInput[]
  cursor?: Prisma.ReviewWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ReviewScalarFieldEnum | Prisma.ReviewScalarFieldEnum[]
}

/**
 * Project.aiAnalysis
 */
export type Project$aiAnalysisArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AIAnalysis
   */
  select?: Prisma.AIAnalysisSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AIAnalysis
   */
  omit?: Prisma.AIAnalysisOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AIAnalysisInclude<ExtArgs> | null
  where?: Prisma.AIAnalysisWhereInput
}

/**
 * Project without action
 */
export type ProjectDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Project
   */
  select?: Prisma.ProjectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Project
   */
  omit?: Prisma.ProjectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProjectInclude<ExtArgs> | null
}
