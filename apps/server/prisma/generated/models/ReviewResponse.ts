
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `ReviewResponse` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums.js"
import type * as Prisma from "../internal/prismaNamespace.js"

/**
 * Model ReviewResponse
 * 
 */
export type ReviewResponseModel = runtime.Types.Result.DefaultSelection<Prisma.$ReviewResponsePayload>

export type AggregateReviewResponse = {
  _count: ReviewResponseCountAggregateOutputType | null
  _avg: ReviewResponseAvgAggregateOutputType | null
  _sum: ReviewResponseSumAggregateOutputType | null
  _min: ReviewResponseMinAggregateOutputType | null
  _max: ReviewResponseMaxAggregateOutputType | null
}

export type ReviewResponseAvgAggregateOutputType = {
  questionIndex: number | null
  relevanceScore: number | null
  aiConfidence: number | null
}

export type ReviewResponseSumAggregateOutputType = {
  questionIndex: number | null
  relevanceScore: number | null
  aiConfidence: number | null
}

export type ReviewResponseMinAggregateOutputType = {
  id: string | null
  reviewId: string | null
  dimension: $Enums.ReviewDimension | null
  questionIndex: number | null
  vote: boolean | null
  feedback: string | null
  feedbackRelevant: boolean | null
  relevanceScore: number | null
  validationReason: string | null
  aiConfidence: number | null
}

export type ReviewResponseMaxAggregateOutputType = {
  id: string | null
  reviewId: string | null
  dimension: $Enums.ReviewDimension | null
  questionIndex: number | null
  vote: boolean | null
  feedback: string | null
  feedbackRelevant: boolean | null
  relevanceScore: number | null
  validationReason: string | null
  aiConfidence: number | null
}

export type ReviewResponseCountAggregateOutputType = {
  id: number
  reviewId: number
  dimension: number
  questionIndex: number
  vote: number
  feedback: number
  feedbackRelevant: number
  relevanceScore: number
  validationReason: number
  aiConfidence: number
  _all: number
}


export type ReviewResponseAvgAggregateInputType = {
  questionIndex?: true
  relevanceScore?: true
  aiConfidence?: true
}

export type ReviewResponseSumAggregateInputType = {
  questionIndex?: true
  relevanceScore?: true
  aiConfidence?: true
}

export type ReviewResponseMinAggregateInputType = {
  id?: true
  reviewId?: true
  dimension?: true
  questionIndex?: true
  vote?: true
  feedback?: true
  feedbackRelevant?: true
  relevanceScore?: true
  validationReason?: true
  aiConfidence?: true
}

export type ReviewResponseMaxAggregateInputType = {
  id?: true
  reviewId?: true
  dimension?: true
  questionIndex?: true
  vote?: true
  feedback?: true
  feedbackRelevant?: true
  relevanceScore?: true
  validationReason?: true
  aiConfidence?: true
}

export type ReviewResponseCountAggregateInputType = {
  id?: true
  reviewId?: true
  dimension?: true
  questionIndex?: true
  vote?: true
  feedback?: true
  feedbackRelevant?: true
  relevanceScore?: true
  validationReason?: true
  aiConfidence?: true
  _all?: true
}

export type ReviewResponseAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which ReviewResponse to aggregate.
   */
  where?: Prisma.ReviewResponseWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ReviewResponses to fetch.
   */
  orderBy?: Prisma.ReviewResponseOrderByWithRelationInput | Prisma.ReviewResponseOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.ReviewResponseWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ReviewResponses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ReviewResponses.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned ReviewResponses
  **/
  _count?: true | ReviewResponseCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: ReviewResponseAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: ReviewResponseSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: ReviewResponseMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: ReviewResponseMaxAggregateInputType
}

export type GetReviewResponseAggregateType<T extends ReviewResponseAggregateArgs> = {
      [P in keyof T & keyof AggregateReviewResponse]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateReviewResponse[P]>
    : Prisma.GetScalarType<T[P], AggregateReviewResponse[P]>
}




export type ReviewResponseGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ReviewResponseWhereInput
  orderBy?: Prisma.ReviewResponseOrderByWithAggregationInput | Prisma.ReviewResponseOrderByWithAggregationInput[]
  by: Prisma.ReviewResponseScalarFieldEnum[] | Prisma.ReviewResponseScalarFieldEnum
  having?: Prisma.ReviewResponseScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: ReviewResponseCountAggregateInputType | true
  _avg?: ReviewResponseAvgAggregateInputType
  _sum?: ReviewResponseSumAggregateInputType
  _min?: ReviewResponseMinAggregateInputType
  _max?: ReviewResponseMaxAggregateInputType
}

export type ReviewResponseGroupByOutputType = {
  id: string
  reviewId: string
  dimension: $Enums.ReviewDimension
  questionIndex: number
  vote: boolean
  feedback: string | null
  feedbackRelevant: boolean
  relevanceScore: number
  validationReason: string | null
  aiConfidence: number
  _count: ReviewResponseCountAggregateOutputType | null
  _avg: ReviewResponseAvgAggregateOutputType | null
  _sum: ReviewResponseSumAggregateOutputType | null
  _min: ReviewResponseMinAggregateOutputType | null
  _max: ReviewResponseMaxAggregateOutputType | null
}

type GetReviewResponseGroupByPayload<T extends ReviewResponseGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<ReviewResponseGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof ReviewResponseGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], ReviewResponseGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], ReviewResponseGroupByOutputType[P]>
      }
    >
  > 



export type ReviewResponseWhereInput = {
  AND?: Prisma.ReviewResponseWhereInput | Prisma.ReviewResponseWhereInput[]
  OR?: Prisma.ReviewResponseWhereInput[]
  NOT?: Prisma.ReviewResponseWhereInput | Prisma.ReviewResponseWhereInput[]
  id?: Prisma.StringFilter<"ReviewResponse"> | string
  reviewId?: Prisma.StringFilter<"ReviewResponse"> | string
  dimension?: Prisma.EnumReviewDimensionFilter<"ReviewResponse"> | $Enums.ReviewDimension
  questionIndex?: Prisma.IntFilter<"ReviewResponse"> | number
  vote?: Prisma.BoolFilter<"ReviewResponse"> | boolean
  feedback?: Prisma.StringNullableFilter<"ReviewResponse"> | string | null
  feedbackRelevant?: Prisma.BoolFilter<"ReviewResponse"> | boolean
  relevanceScore?: Prisma.IntFilter<"ReviewResponse"> | number
  validationReason?: Prisma.StringNullableFilter<"ReviewResponse"> | string | null
  aiConfidence?: Prisma.IntFilter<"ReviewResponse"> | number
  review?: Prisma.XOR<Prisma.ReviewScalarRelationFilter, Prisma.ReviewWhereInput>
}

export type ReviewResponseOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  reviewId?: Prisma.SortOrder
  dimension?: Prisma.SortOrder
  questionIndex?: Prisma.SortOrder
  vote?: Prisma.SortOrder
  feedback?: Prisma.SortOrderInput | Prisma.SortOrder
  feedbackRelevant?: Prisma.SortOrder
  relevanceScore?: Prisma.SortOrder
  validationReason?: Prisma.SortOrderInput | Prisma.SortOrder
  aiConfidence?: Prisma.SortOrder
  review?: Prisma.ReviewOrderByWithRelationInput
}

export type ReviewResponseWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  reviewId_dimension_questionIndex?: Prisma.ReviewResponseReviewIdDimensionQuestionIndexCompoundUniqueInput
  AND?: Prisma.ReviewResponseWhereInput | Prisma.ReviewResponseWhereInput[]
  OR?: Prisma.ReviewResponseWhereInput[]
  NOT?: Prisma.ReviewResponseWhereInput | Prisma.ReviewResponseWhereInput[]
  reviewId?: Prisma.StringFilter<"ReviewResponse"> | string
  dimension?: Prisma.EnumReviewDimensionFilter<"ReviewResponse"> | $Enums.ReviewDimension
  questionIndex?: Prisma.IntFilter<"ReviewResponse"> | number
  vote?: Prisma.BoolFilter<"ReviewResponse"> | boolean
  feedback?: Prisma.StringNullableFilter<"ReviewResponse"> | string | null
  feedbackRelevant?: Prisma.BoolFilter<"ReviewResponse"> | boolean
  relevanceScore?: Prisma.IntFilter<"ReviewResponse"> | number
  validationReason?: Prisma.StringNullableFilter<"ReviewResponse"> | string | null
  aiConfidence?: Prisma.IntFilter<"ReviewResponse"> | number
  review?: Prisma.XOR<Prisma.ReviewScalarRelationFilter, Prisma.ReviewWhereInput>
}, "id" | "reviewId_dimension_questionIndex">

export type ReviewResponseOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  reviewId?: Prisma.SortOrder
  dimension?: Prisma.SortOrder
  questionIndex?: Prisma.SortOrder
  vote?: Prisma.SortOrder
  feedback?: Prisma.SortOrderInput | Prisma.SortOrder
  feedbackRelevant?: Prisma.SortOrder
  relevanceScore?: Prisma.SortOrder
  validationReason?: Prisma.SortOrderInput | Prisma.SortOrder
  aiConfidence?: Prisma.SortOrder
  _count?: Prisma.ReviewResponseCountOrderByAggregateInput
  _avg?: Prisma.ReviewResponseAvgOrderByAggregateInput
  _max?: Prisma.ReviewResponseMaxOrderByAggregateInput
  _min?: Prisma.ReviewResponseMinOrderByAggregateInput
  _sum?: Prisma.ReviewResponseSumOrderByAggregateInput
}

export type ReviewResponseScalarWhereWithAggregatesInput = {
  AND?: Prisma.ReviewResponseScalarWhereWithAggregatesInput | Prisma.ReviewResponseScalarWhereWithAggregatesInput[]
  OR?: Prisma.ReviewResponseScalarWhereWithAggregatesInput[]
  NOT?: Prisma.ReviewResponseScalarWhereWithAggregatesInput | Prisma.ReviewResponseScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"ReviewResponse"> | string
  reviewId?: Prisma.StringWithAggregatesFilter<"ReviewResponse"> | string
  dimension?: Prisma.EnumReviewDimensionWithAggregatesFilter<"ReviewResponse"> | $Enums.ReviewDimension
  questionIndex?: Prisma.IntWithAggregatesFilter<"ReviewResponse"> | number
  vote?: Prisma.BoolWithAggregatesFilter<"ReviewResponse"> | boolean
  feedback?: Prisma.StringNullableWithAggregatesFilter<"ReviewResponse"> | string | null
  feedbackRelevant?: Prisma.BoolWithAggregatesFilter<"ReviewResponse"> | boolean
  relevanceScore?: Prisma.IntWithAggregatesFilter<"ReviewResponse"> | number
  validationReason?: Prisma.StringNullableWithAggregatesFilter<"ReviewResponse"> | string | null
  aiConfidence?: Prisma.IntWithAggregatesFilter<"ReviewResponse"> | number
}

export type ReviewResponseCreateInput = {
  id?: string
  dimension: $Enums.ReviewDimension
  questionIndex: number
  vote: boolean
  feedback?: string | null
  feedbackRelevant?: boolean
  relevanceScore?: number
  validationReason?: string | null
  aiConfidence?: number
  review: Prisma.ReviewCreateNestedOneWithoutResponsesInput
}

export type ReviewResponseUncheckedCreateInput = {
  id?: string
  reviewId: string
  dimension: $Enums.ReviewDimension
  questionIndex: number
  vote: boolean
  feedback?: string | null
  feedbackRelevant?: boolean
  relevanceScore?: number
  validationReason?: string | null
  aiConfidence?: number
}

export type ReviewResponseUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  dimension?: Prisma.EnumReviewDimensionFieldUpdateOperationsInput | $Enums.ReviewDimension
  questionIndex?: Prisma.IntFieldUpdateOperationsInput | number
  vote?: Prisma.BoolFieldUpdateOperationsInput | boolean
  feedback?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  feedbackRelevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
  relevanceScore?: Prisma.IntFieldUpdateOperationsInput | number
  validationReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  aiConfidence?: Prisma.IntFieldUpdateOperationsInput | number
  review?: Prisma.ReviewUpdateOneRequiredWithoutResponsesNestedInput
}

export type ReviewResponseUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  reviewId?: Prisma.StringFieldUpdateOperationsInput | string
  dimension?: Prisma.EnumReviewDimensionFieldUpdateOperationsInput | $Enums.ReviewDimension
  questionIndex?: Prisma.IntFieldUpdateOperationsInput | number
  vote?: Prisma.BoolFieldUpdateOperationsInput | boolean
  feedback?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  feedbackRelevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
  relevanceScore?: Prisma.IntFieldUpdateOperationsInput | number
  validationReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  aiConfidence?: Prisma.IntFieldUpdateOperationsInput | number
}

export type ReviewResponseCreateManyInput = {
  id?: string
  reviewId: string
  dimension: $Enums.ReviewDimension
  questionIndex: number
  vote: boolean
  feedback?: string | null
  feedbackRelevant?: boolean
  relevanceScore?: number
  validationReason?: string | null
  aiConfidence?: number
}

export type ReviewResponseUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  dimension?: Prisma.EnumReviewDimensionFieldUpdateOperationsInput | $Enums.ReviewDimension
  questionIndex?: Prisma.IntFieldUpdateOperationsInput | number
  vote?: Prisma.BoolFieldUpdateOperationsInput | boolean
  feedback?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  feedbackRelevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
  relevanceScore?: Prisma.IntFieldUpdateOperationsInput | number
  validationReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  aiConfidence?: Prisma.IntFieldUpdateOperationsInput | number
}

export type ReviewResponseUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  reviewId?: Prisma.StringFieldUpdateOperationsInput | string
  dimension?: Prisma.EnumReviewDimensionFieldUpdateOperationsInput | $Enums.ReviewDimension
  questionIndex?: Prisma.IntFieldUpdateOperationsInput | number
  vote?: Prisma.BoolFieldUpdateOperationsInput | boolean
  feedback?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  feedbackRelevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
  relevanceScore?: Prisma.IntFieldUpdateOperationsInput | number
  validationReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  aiConfidence?: Prisma.IntFieldUpdateOperationsInput | number
}

export type ReviewResponseListRelationFilter = {
  every?: Prisma.ReviewResponseWhereInput
  some?: Prisma.ReviewResponseWhereInput
  none?: Prisma.ReviewResponseWhereInput
}

export type ReviewResponseOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type ReviewResponseReviewIdDimensionQuestionIndexCompoundUniqueInput = {
  reviewId: string
  dimension: $Enums.ReviewDimension
  questionIndex: number
}

export type ReviewResponseCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  reviewId?: Prisma.SortOrder
  dimension?: Prisma.SortOrder
  questionIndex?: Prisma.SortOrder
  vote?: Prisma.SortOrder
  feedback?: Prisma.SortOrder
  feedbackRelevant?: Prisma.SortOrder
  relevanceScore?: Prisma.SortOrder
  validationReason?: Prisma.SortOrder
  aiConfidence?: Prisma.SortOrder
}

export type ReviewResponseAvgOrderByAggregateInput = {
  questionIndex?: Prisma.SortOrder
  relevanceScore?: Prisma.SortOrder
  aiConfidence?: Prisma.SortOrder
}

export type ReviewResponseMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  reviewId?: Prisma.SortOrder
  dimension?: Prisma.SortOrder
  questionIndex?: Prisma.SortOrder
  vote?: Prisma.SortOrder
  feedback?: Prisma.SortOrder
  feedbackRelevant?: Prisma.SortOrder
  relevanceScore?: Prisma.SortOrder
  validationReason?: Prisma.SortOrder
  aiConfidence?: Prisma.SortOrder
}

export type ReviewResponseMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  reviewId?: Prisma.SortOrder
  dimension?: Prisma.SortOrder
  questionIndex?: Prisma.SortOrder
  vote?: Prisma.SortOrder
  feedback?: Prisma.SortOrder
  feedbackRelevant?: Prisma.SortOrder
  relevanceScore?: Prisma.SortOrder
  validationReason?: Prisma.SortOrder
  aiConfidence?: Prisma.SortOrder
}

export type ReviewResponseSumOrderByAggregateInput = {
  questionIndex?: Prisma.SortOrder
  relevanceScore?: Prisma.SortOrder
  aiConfidence?: Prisma.SortOrder
}

export type ReviewResponseCreateNestedManyWithoutReviewInput = {
  create?: Prisma.XOR<Prisma.ReviewResponseCreateWithoutReviewInput, Prisma.ReviewResponseUncheckedCreateWithoutReviewInput> | Prisma.ReviewResponseCreateWithoutReviewInput[] | Prisma.ReviewResponseUncheckedCreateWithoutReviewInput[]
  connectOrCreate?: Prisma.ReviewResponseCreateOrConnectWithoutReviewInput | Prisma.ReviewResponseCreateOrConnectWithoutReviewInput[]
  createMany?: Prisma.ReviewResponseCreateManyReviewInputEnvelope
  connect?: Prisma.ReviewResponseWhereUniqueInput | Prisma.ReviewResponseWhereUniqueInput[]
}

export type ReviewResponseUncheckedCreateNestedManyWithoutReviewInput = {
  create?: Prisma.XOR<Prisma.ReviewResponseCreateWithoutReviewInput, Prisma.ReviewResponseUncheckedCreateWithoutReviewInput> | Prisma.ReviewResponseCreateWithoutReviewInput[] | Prisma.ReviewResponseUncheckedCreateWithoutReviewInput[]
  connectOrCreate?: Prisma.ReviewResponseCreateOrConnectWithoutReviewInput | Prisma.ReviewResponseCreateOrConnectWithoutReviewInput[]
  createMany?: Prisma.ReviewResponseCreateManyReviewInputEnvelope
  connect?: Prisma.ReviewResponseWhereUniqueInput | Prisma.ReviewResponseWhereUniqueInput[]
}

export type ReviewResponseUpdateManyWithoutReviewNestedInput = {
  create?: Prisma.XOR<Prisma.ReviewResponseCreateWithoutReviewInput, Prisma.ReviewResponseUncheckedCreateWithoutReviewInput> | Prisma.ReviewResponseCreateWithoutReviewInput[] | Prisma.ReviewResponseUncheckedCreateWithoutReviewInput[]
  connectOrCreate?: Prisma.ReviewResponseCreateOrConnectWithoutReviewInput | Prisma.ReviewResponseCreateOrConnectWithoutReviewInput[]
  upsert?: Prisma.ReviewResponseUpsertWithWhereUniqueWithoutReviewInput | Prisma.ReviewResponseUpsertWithWhereUniqueWithoutReviewInput[]
  createMany?: Prisma.ReviewResponseCreateManyReviewInputEnvelope
  set?: Prisma.ReviewResponseWhereUniqueInput | Prisma.ReviewResponseWhereUniqueInput[]
  disconnect?: Prisma.ReviewResponseWhereUniqueInput | Prisma.ReviewResponseWhereUniqueInput[]
  delete?: Prisma.ReviewResponseWhereUniqueInput | Prisma.ReviewResponseWhereUniqueInput[]
  connect?: Prisma.ReviewResponseWhereUniqueInput | Prisma.ReviewResponseWhereUniqueInput[]
  update?: Prisma.ReviewResponseUpdateWithWhereUniqueWithoutReviewInput | Prisma.ReviewResponseUpdateWithWhereUniqueWithoutReviewInput[]
  updateMany?: Prisma.ReviewResponseUpdateManyWithWhereWithoutReviewInput | Prisma.ReviewResponseUpdateManyWithWhereWithoutReviewInput[]
  deleteMany?: Prisma.ReviewResponseScalarWhereInput | Prisma.ReviewResponseScalarWhereInput[]
}

export type ReviewResponseUncheckedUpdateManyWithoutReviewNestedInput = {
  create?: Prisma.XOR<Prisma.ReviewResponseCreateWithoutReviewInput, Prisma.ReviewResponseUncheckedCreateWithoutReviewInput> | Prisma.ReviewResponseCreateWithoutReviewInput[] | Prisma.ReviewResponseUncheckedCreateWithoutReviewInput[]
  connectOrCreate?: Prisma.ReviewResponseCreateOrConnectWithoutReviewInput | Prisma.ReviewResponseCreateOrConnectWithoutReviewInput[]
  upsert?: Prisma.ReviewResponseUpsertWithWhereUniqueWithoutReviewInput | Prisma.ReviewResponseUpsertWithWhereUniqueWithoutReviewInput[]
  createMany?: Prisma.ReviewResponseCreateManyReviewInputEnvelope
  set?: Prisma.ReviewResponseWhereUniqueInput | Prisma.ReviewResponseWhereUniqueInput[]
  disconnect?: Prisma.ReviewResponseWhereUniqueInput | Prisma.ReviewResponseWhereUniqueInput[]
  delete?: Prisma.ReviewResponseWhereUniqueInput | Prisma.ReviewResponseWhereUniqueInput[]
  connect?: Prisma.ReviewResponseWhereUniqueInput | Prisma.ReviewResponseWhereUniqueInput[]
  update?: Prisma.ReviewResponseUpdateWithWhereUniqueWithoutReviewInput | Prisma.ReviewResponseUpdateWithWhereUniqueWithoutReviewInput[]
  updateMany?: Prisma.ReviewResponseUpdateManyWithWhereWithoutReviewInput | Prisma.ReviewResponseUpdateManyWithWhereWithoutReviewInput[]
  deleteMany?: Prisma.ReviewResponseScalarWhereInput | Prisma.ReviewResponseScalarWhereInput[]
}

export type EnumReviewDimensionFieldUpdateOperationsInput = {
  set?: $Enums.ReviewDimension
}

export type ReviewResponseCreateWithoutReviewInput = {
  id?: string
  dimension: $Enums.ReviewDimension
  questionIndex: number
  vote: boolean
  feedback?: string | null
  feedbackRelevant?: boolean
  relevanceScore?: number
  validationReason?: string | null
  aiConfidence?: number
}

export type ReviewResponseUncheckedCreateWithoutReviewInput = {
  id?: string
  dimension: $Enums.ReviewDimension
  questionIndex: number
  vote: boolean
  feedback?: string | null
  feedbackRelevant?: boolean
  relevanceScore?: number
  validationReason?: string | null
  aiConfidence?: number
}

export type ReviewResponseCreateOrConnectWithoutReviewInput = {
  where: Prisma.ReviewResponseWhereUniqueInput
  create: Prisma.XOR<Prisma.ReviewResponseCreateWithoutReviewInput, Prisma.ReviewResponseUncheckedCreateWithoutReviewInput>
}

export type ReviewResponseCreateManyReviewInputEnvelope = {
  data: Prisma.ReviewResponseCreateManyReviewInput | Prisma.ReviewResponseCreateManyReviewInput[]
  skipDuplicates?: boolean
}

export type ReviewResponseUpsertWithWhereUniqueWithoutReviewInput = {
  where: Prisma.ReviewResponseWhereUniqueInput
  update: Prisma.XOR<Prisma.ReviewResponseUpdateWithoutReviewInput, Prisma.ReviewResponseUncheckedUpdateWithoutReviewInput>
  create: Prisma.XOR<Prisma.ReviewResponseCreateWithoutReviewInput, Prisma.ReviewResponseUncheckedCreateWithoutReviewInput>
}

export type ReviewResponseUpdateWithWhereUniqueWithoutReviewInput = {
  where: Prisma.ReviewResponseWhereUniqueInput
  data: Prisma.XOR<Prisma.ReviewResponseUpdateWithoutReviewInput, Prisma.ReviewResponseUncheckedUpdateWithoutReviewInput>
}

export type ReviewResponseUpdateManyWithWhereWithoutReviewInput = {
  where: Prisma.ReviewResponseScalarWhereInput
  data: Prisma.XOR<Prisma.ReviewResponseUpdateManyMutationInput, Prisma.ReviewResponseUncheckedUpdateManyWithoutReviewInput>
}

export type ReviewResponseScalarWhereInput = {
  AND?: Prisma.ReviewResponseScalarWhereInput | Prisma.ReviewResponseScalarWhereInput[]
  OR?: Prisma.ReviewResponseScalarWhereInput[]
  NOT?: Prisma.ReviewResponseScalarWhereInput | Prisma.ReviewResponseScalarWhereInput[]
  id?: Prisma.StringFilter<"ReviewResponse"> | string
  reviewId?: Prisma.StringFilter<"ReviewResponse"> | string
  dimension?: Prisma.EnumReviewDimensionFilter<"ReviewResponse"> | $Enums.ReviewDimension
  questionIndex?: Prisma.IntFilter<"ReviewResponse"> | number
  vote?: Prisma.BoolFilter<"ReviewResponse"> | boolean
  feedback?: Prisma.StringNullableFilter<"ReviewResponse"> | string | null
  feedbackRelevant?: Prisma.BoolFilter<"ReviewResponse"> | boolean
  relevanceScore?: Prisma.IntFilter<"ReviewResponse"> | number
  validationReason?: Prisma.StringNullableFilter<"ReviewResponse"> | string | null
  aiConfidence?: Prisma.IntFilter<"ReviewResponse"> | number
}

export type ReviewResponseCreateManyReviewInput = {
  id?: string
  dimension: $Enums.ReviewDimension
  questionIndex: number
  vote: boolean
  feedback?: string | null
  feedbackRelevant?: boolean
  relevanceScore?: number
  validationReason?: string | null
  aiConfidence?: number
}

export type ReviewResponseUpdateWithoutReviewInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  dimension?: Prisma.EnumReviewDimensionFieldUpdateOperationsInput | $Enums.ReviewDimension
  questionIndex?: Prisma.IntFieldUpdateOperationsInput | number
  vote?: Prisma.BoolFieldUpdateOperationsInput | boolean
  feedback?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  feedbackRelevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
  relevanceScore?: Prisma.IntFieldUpdateOperationsInput | number
  validationReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  aiConfidence?: Prisma.IntFieldUpdateOperationsInput | number
}

export type ReviewResponseUncheckedUpdateWithoutReviewInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  dimension?: Prisma.EnumReviewDimensionFieldUpdateOperationsInput | $Enums.ReviewDimension
  questionIndex?: Prisma.IntFieldUpdateOperationsInput | number
  vote?: Prisma.BoolFieldUpdateOperationsInput | boolean
  feedback?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  feedbackRelevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
  relevanceScore?: Prisma.IntFieldUpdateOperationsInput | number
  validationReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  aiConfidence?: Prisma.IntFieldUpdateOperationsInput | number
}

export type ReviewResponseUncheckedUpdateManyWithoutReviewInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  dimension?: Prisma.EnumReviewDimensionFieldUpdateOperationsInput | $Enums.ReviewDimension
  questionIndex?: Prisma.IntFieldUpdateOperationsInput | number
  vote?: Prisma.BoolFieldUpdateOperationsInput | boolean
  feedback?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  feedbackRelevant?: Prisma.BoolFieldUpdateOperationsInput | boolean
  relevanceScore?: Prisma.IntFieldUpdateOperationsInput | number
  validationReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  aiConfidence?: Prisma.IntFieldUpdateOperationsInput | number
}



export type ReviewResponseSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  reviewId?: boolean
  dimension?: boolean
  questionIndex?: boolean
  vote?: boolean
  feedback?: boolean
  feedbackRelevant?: boolean
  relevanceScore?: boolean
  validationReason?: boolean
  aiConfidence?: boolean
  review?: boolean | Prisma.ReviewDefaultArgs<ExtArgs>
}, ExtArgs["result"]["reviewResponse"]>

export type ReviewResponseSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  reviewId?: boolean
  dimension?: boolean
  questionIndex?: boolean
  vote?: boolean
  feedback?: boolean
  feedbackRelevant?: boolean
  relevanceScore?: boolean
  validationReason?: boolean
  aiConfidence?: boolean
  review?: boolean | Prisma.ReviewDefaultArgs<ExtArgs>
}, ExtArgs["result"]["reviewResponse"]>

export type ReviewResponseSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  reviewId?: boolean
  dimension?: boolean
  questionIndex?: boolean
  vote?: boolean
  feedback?: boolean
  feedbackRelevant?: boolean
  relevanceScore?: boolean
  validationReason?: boolean
  aiConfidence?: boolean
  review?: boolean | Prisma.ReviewDefaultArgs<ExtArgs>
}, ExtArgs["result"]["reviewResponse"]>

export type ReviewResponseSelectScalar = {
  id?: boolean
  reviewId?: boolean
  dimension?: boolean
  questionIndex?: boolean
  vote?: boolean
  feedback?: boolean
  feedbackRelevant?: boolean
  relevanceScore?: boolean
  validationReason?: boolean
  aiConfidence?: boolean
}

export type ReviewResponseOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "reviewId" | "dimension" | "questionIndex" | "vote" | "feedback" | "feedbackRelevant" | "relevanceScore" | "validationReason" | "aiConfidence", ExtArgs["result"]["reviewResponse"]>
export type ReviewResponseInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  review?: boolean | Prisma.ReviewDefaultArgs<ExtArgs>
}
export type ReviewResponseIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  review?: boolean | Prisma.ReviewDefaultArgs<ExtArgs>
}
export type ReviewResponseIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  review?: boolean | Prisma.ReviewDefaultArgs<ExtArgs>
}

export type $ReviewResponsePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "ReviewResponse"
  objects: {
    review: Prisma.$ReviewPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    reviewId: string
    dimension: $Enums.ReviewDimension
    questionIndex: number
    vote: boolean
    feedback: string | null
    feedbackRelevant: boolean
    relevanceScore: number
    validationReason: string | null
    aiConfidence: number
  }, ExtArgs["result"]["reviewResponse"]>
  composites: {}
}

export type ReviewResponseGetPayload<S extends boolean | null | undefined | ReviewResponseDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$ReviewResponsePayload, S>

export type ReviewResponseCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<ReviewResponseFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: ReviewResponseCountAggregateInputType | true
  }

export interface ReviewResponseDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['ReviewResponse'], meta: { name: 'ReviewResponse' } }
  /**
   * Find zero or one ReviewResponse that matches the filter.
   * @param {ReviewResponseFindUniqueArgs} args - Arguments to find a ReviewResponse
   * @example
   * // Get one ReviewResponse
   * const reviewResponse = await prisma.reviewResponse.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends ReviewResponseFindUniqueArgs>(args: Prisma.SelectSubset<T, ReviewResponseFindUniqueArgs<ExtArgs>>): Prisma.Prisma__ReviewResponseClient<runtime.Types.Result.GetResult<Prisma.$ReviewResponsePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one ReviewResponse that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {ReviewResponseFindUniqueOrThrowArgs} args - Arguments to find a ReviewResponse
   * @example
   * // Get one ReviewResponse
   * const reviewResponse = await prisma.reviewResponse.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends ReviewResponseFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, ReviewResponseFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__ReviewResponseClient<runtime.Types.Result.GetResult<Prisma.$ReviewResponsePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first ReviewResponse that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReviewResponseFindFirstArgs} args - Arguments to find a ReviewResponse
   * @example
   * // Get one ReviewResponse
   * const reviewResponse = await prisma.reviewResponse.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends ReviewResponseFindFirstArgs>(args?: Prisma.SelectSubset<T, ReviewResponseFindFirstArgs<ExtArgs>>): Prisma.Prisma__ReviewResponseClient<runtime.Types.Result.GetResult<Prisma.$ReviewResponsePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first ReviewResponse that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReviewResponseFindFirstOrThrowArgs} args - Arguments to find a ReviewResponse
   * @example
   * // Get one ReviewResponse
   * const reviewResponse = await prisma.reviewResponse.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends ReviewResponseFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, ReviewResponseFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__ReviewResponseClient<runtime.Types.Result.GetResult<Prisma.$ReviewResponsePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more ReviewResponses that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReviewResponseFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all ReviewResponses
   * const reviewResponses = await prisma.reviewResponse.findMany()
   * 
   * // Get first 10 ReviewResponses
   * const reviewResponses = await prisma.reviewResponse.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const reviewResponseWithIdOnly = await prisma.reviewResponse.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends ReviewResponseFindManyArgs>(args?: Prisma.SelectSubset<T, ReviewResponseFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ReviewResponsePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a ReviewResponse.
   * @param {ReviewResponseCreateArgs} args - Arguments to create a ReviewResponse.
   * @example
   * // Create one ReviewResponse
   * const ReviewResponse = await prisma.reviewResponse.create({
   *   data: {
   *     // ... data to create a ReviewResponse
   *   }
   * })
   * 
   */
  create<T extends ReviewResponseCreateArgs>(args: Prisma.SelectSubset<T, ReviewResponseCreateArgs<ExtArgs>>): Prisma.Prisma__ReviewResponseClient<runtime.Types.Result.GetResult<Prisma.$ReviewResponsePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many ReviewResponses.
   * @param {ReviewResponseCreateManyArgs} args - Arguments to create many ReviewResponses.
   * @example
   * // Create many ReviewResponses
   * const reviewResponse = await prisma.reviewResponse.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends ReviewResponseCreateManyArgs>(args?: Prisma.SelectSubset<T, ReviewResponseCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many ReviewResponses and returns the data saved in the database.
   * @param {ReviewResponseCreateManyAndReturnArgs} args - Arguments to create many ReviewResponses.
   * @example
   * // Create many ReviewResponses
   * const reviewResponse = await prisma.reviewResponse.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many ReviewResponses and only return the `id`
   * const reviewResponseWithIdOnly = await prisma.reviewResponse.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends ReviewResponseCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, ReviewResponseCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ReviewResponsePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a ReviewResponse.
   * @param {ReviewResponseDeleteArgs} args - Arguments to delete one ReviewResponse.
   * @example
   * // Delete one ReviewResponse
   * const ReviewResponse = await prisma.reviewResponse.delete({
   *   where: {
   *     // ... filter to delete one ReviewResponse
   *   }
   * })
   * 
   */
  delete<T extends ReviewResponseDeleteArgs>(args: Prisma.SelectSubset<T, ReviewResponseDeleteArgs<ExtArgs>>): Prisma.Prisma__ReviewResponseClient<runtime.Types.Result.GetResult<Prisma.$ReviewResponsePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one ReviewResponse.
   * @param {ReviewResponseUpdateArgs} args - Arguments to update one ReviewResponse.
   * @example
   * // Update one ReviewResponse
   * const reviewResponse = await prisma.reviewResponse.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends ReviewResponseUpdateArgs>(args: Prisma.SelectSubset<T, ReviewResponseUpdateArgs<ExtArgs>>): Prisma.Prisma__ReviewResponseClient<runtime.Types.Result.GetResult<Prisma.$ReviewResponsePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more ReviewResponses.
   * @param {ReviewResponseDeleteManyArgs} args - Arguments to filter ReviewResponses to delete.
   * @example
   * // Delete a few ReviewResponses
   * const { count } = await prisma.reviewResponse.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends ReviewResponseDeleteManyArgs>(args?: Prisma.SelectSubset<T, ReviewResponseDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more ReviewResponses.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReviewResponseUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many ReviewResponses
   * const reviewResponse = await prisma.reviewResponse.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends ReviewResponseUpdateManyArgs>(args: Prisma.SelectSubset<T, ReviewResponseUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more ReviewResponses and returns the data updated in the database.
   * @param {ReviewResponseUpdateManyAndReturnArgs} args - Arguments to update many ReviewResponses.
   * @example
   * // Update many ReviewResponses
   * const reviewResponse = await prisma.reviewResponse.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more ReviewResponses and only return the `id`
   * const reviewResponseWithIdOnly = await prisma.reviewResponse.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends ReviewResponseUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, ReviewResponseUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ReviewResponsePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one ReviewResponse.
   * @param {ReviewResponseUpsertArgs} args - Arguments to update or create a ReviewResponse.
   * @example
   * // Update or create a ReviewResponse
   * const reviewResponse = await prisma.reviewResponse.upsert({
   *   create: {
   *     // ... data to create a ReviewResponse
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the ReviewResponse we want to update
   *   }
   * })
   */
  upsert<T extends ReviewResponseUpsertArgs>(args: Prisma.SelectSubset<T, ReviewResponseUpsertArgs<ExtArgs>>): Prisma.Prisma__ReviewResponseClient<runtime.Types.Result.GetResult<Prisma.$ReviewResponsePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of ReviewResponses.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReviewResponseCountArgs} args - Arguments to filter ReviewResponses to count.
   * @example
   * // Count the number of ReviewResponses
   * const count = await prisma.reviewResponse.count({
   *   where: {
   *     // ... the filter for the ReviewResponses we want to count
   *   }
   * })
  **/
  count<T extends ReviewResponseCountArgs>(
    args?: Prisma.Subset<T, ReviewResponseCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], ReviewResponseCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a ReviewResponse.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReviewResponseAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends ReviewResponseAggregateArgs>(args: Prisma.Subset<T, ReviewResponseAggregateArgs>): Prisma.PrismaPromise<GetReviewResponseAggregateType<T>>

  /**
   * Group by ReviewResponse.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReviewResponseGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends ReviewResponseGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: ReviewResponseGroupByArgs['orderBy'] }
      : { orderBy?: ReviewResponseGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, ReviewResponseGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetReviewResponseGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the ReviewResponse model
 */
readonly fields: ReviewResponseFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for ReviewResponse.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__ReviewResponseClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  review<T extends Prisma.ReviewDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.ReviewDefaultArgs<ExtArgs>>): Prisma.Prisma__ReviewClient<runtime.Types.Result.GetResult<Prisma.$ReviewPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the ReviewResponse model
 */
export interface ReviewResponseFieldRefs {
  readonly id: Prisma.FieldRef<"ReviewResponse", 'String'>
  readonly reviewId: Prisma.FieldRef<"ReviewResponse", 'String'>
  readonly dimension: Prisma.FieldRef<"ReviewResponse", 'ReviewDimension'>
  readonly questionIndex: Prisma.FieldRef<"ReviewResponse", 'Int'>
  readonly vote: Prisma.FieldRef<"ReviewResponse", 'Boolean'>
  readonly feedback: Prisma.FieldRef<"ReviewResponse", 'String'>
  readonly feedbackRelevant: Prisma.FieldRef<"ReviewResponse", 'Boolean'>
  readonly relevanceScore: Prisma.FieldRef<"ReviewResponse", 'Int'>
  readonly validationReason: Prisma.FieldRef<"ReviewResponse", 'String'>
  readonly aiConfidence: Prisma.FieldRef<"ReviewResponse", 'Int'>
}
    

// Custom InputTypes
/**
 * ReviewResponse findUnique
 */
export type ReviewResponseFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReviewResponse
   */
  select?: Prisma.ReviewResponseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReviewResponse
   */
  omit?: Prisma.ReviewResponseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReviewResponseInclude<ExtArgs> | null
  /**
   * Filter, which ReviewResponse to fetch.
   */
  where: Prisma.ReviewResponseWhereUniqueInput
}

/**
 * ReviewResponse findUniqueOrThrow
 */
export type ReviewResponseFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReviewResponse
   */
  select?: Prisma.ReviewResponseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReviewResponse
   */
  omit?: Prisma.ReviewResponseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReviewResponseInclude<ExtArgs> | null
  /**
   * Filter, which ReviewResponse to fetch.
   */
  where: Prisma.ReviewResponseWhereUniqueInput
}

/**
 * ReviewResponse findFirst
 */
export type ReviewResponseFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReviewResponse
   */
  select?: Prisma.ReviewResponseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReviewResponse
   */
  omit?: Prisma.ReviewResponseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReviewResponseInclude<ExtArgs> | null
  /**
   * Filter, which ReviewResponse to fetch.
   */
  where?: Prisma.ReviewResponseWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ReviewResponses to fetch.
   */
  orderBy?: Prisma.ReviewResponseOrderByWithRelationInput | Prisma.ReviewResponseOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for ReviewResponses.
   */
  cursor?: Prisma.ReviewResponseWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ReviewResponses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ReviewResponses.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of ReviewResponses.
   */
  distinct?: Prisma.ReviewResponseScalarFieldEnum | Prisma.ReviewResponseScalarFieldEnum[]
}

/**
 * ReviewResponse findFirstOrThrow
 */
export type ReviewResponseFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReviewResponse
   */
  select?: Prisma.ReviewResponseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReviewResponse
   */
  omit?: Prisma.ReviewResponseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReviewResponseInclude<ExtArgs> | null
  /**
   * Filter, which ReviewResponse to fetch.
   */
  where?: Prisma.ReviewResponseWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ReviewResponses to fetch.
   */
  orderBy?: Prisma.ReviewResponseOrderByWithRelationInput | Prisma.ReviewResponseOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for ReviewResponses.
   */
  cursor?: Prisma.ReviewResponseWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ReviewResponses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ReviewResponses.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of ReviewResponses.
   */
  distinct?: Prisma.ReviewResponseScalarFieldEnum | Prisma.ReviewResponseScalarFieldEnum[]
}

/**
 * ReviewResponse findMany
 */
export type ReviewResponseFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReviewResponse
   */
  select?: Prisma.ReviewResponseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReviewResponse
   */
  omit?: Prisma.ReviewResponseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReviewResponseInclude<ExtArgs> | null
  /**
   * Filter, which ReviewResponses to fetch.
   */
  where?: Prisma.ReviewResponseWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ReviewResponses to fetch.
   */
  orderBy?: Prisma.ReviewResponseOrderByWithRelationInput | Prisma.ReviewResponseOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing ReviewResponses.
   */
  cursor?: Prisma.ReviewResponseWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ReviewResponses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ReviewResponses.
   */
  skip?: number
  distinct?: Prisma.ReviewResponseScalarFieldEnum | Prisma.ReviewResponseScalarFieldEnum[]
}

/**
 * ReviewResponse create
 */
export type ReviewResponseCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReviewResponse
   */
  select?: Prisma.ReviewResponseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReviewResponse
   */
  omit?: Prisma.ReviewResponseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReviewResponseInclude<ExtArgs> | null
  /**
   * The data needed to create a ReviewResponse.
   */
  data: Prisma.XOR<Prisma.ReviewResponseCreateInput, Prisma.ReviewResponseUncheckedCreateInput>
}

/**
 * ReviewResponse createMany
 */
export type ReviewResponseCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many ReviewResponses.
   */
  data: Prisma.ReviewResponseCreateManyInput | Prisma.ReviewResponseCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * ReviewResponse createManyAndReturn
 */
export type ReviewResponseCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReviewResponse
   */
  select?: Prisma.ReviewResponseSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the ReviewResponse
   */
  omit?: Prisma.ReviewResponseOmit<ExtArgs> | null
  /**
   * The data used to create many ReviewResponses.
   */
  data: Prisma.ReviewResponseCreateManyInput | Prisma.ReviewResponseCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReviewResponseIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * ReviewResponse update
 */
export type ReviewResponseUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReviewResponse
   */
  select?: Prisma.ReviewResponseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReviewResponse
   */
  omit?: Prisma.ReviewResponseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReviewResponseInclude<ExtArgs> | null
  /**
   * The data needed to update a ReviewResponse.
   */
  data: Prisma.XOR<Prisma.ReviewResponseUpdateInput, Prisma.ReviewResponseUncheckedUpdateInput>
  /**
   * Choose, which ReviewResponse to update.
   */
  where: Prisma.ReviewResponseWhereUniqueInput
}

/**
 * ReviewResponse updateMany
 */
export type ReviewResponseUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update ReviewResponses.
   */
  data: Prisma.XOR<Prisma.ReviewResponseUpdateManyMutationInput, Prisma.ReviewResponseUncheckedUpdateManyInput>
  /**
   * Filter which ReviewResponses to update
   */
  where?: Prisma.ReviewResponseWhereInput
  /**
   * Limit how many ReviewResponses to update.
   */
  limit?: number
}

/**
 * ReviewResponse updateManyAndReturn
 */
export type ReviewResponseUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReviewResponse
   */
  select?: Prisma.ReviewResponseSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the ReviewResponse
   */
  omit?: Prisma.ReviewResponseOmit<ExtArgs> | null
  /**
   * The data used to update ReviewResponses.
   */
  data: Prisma.XOR<Prisma.ReviewResponseUpdateManyMutationInput, Prisma.ReviewResponseUncheckedUpdateManyInput>
  /**
   * Filter which ReviewResponses to update
   */
  where?: Prisma.ReviewResponseWhereInput
  /**
   * Limit how many ReviewResponses to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReviewResponseIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * ReviewResponse upsert
 */
export type ReviewResponseUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReviewResponse
   */
  select?: Prisma.ReviewResponseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReviewResponse
   */
  omit?: Prisma.ReviewResponseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReviewResponseInclude<ExtArgs> | null
  /**
   * The filter to search for the ReviewResponse to update in case it exists.
   */
  where: Prisma.ReviewResponseWhereUniqueInput
  /**
   * In case the ReviewResponse found by the `where` argument doesn't exist, create a new ReviewResponse with this data.
   */
  create: Prisma.XOR<Prisma.ReviewResponseCreateInput, Prisma.ReviewResponseUncheckedCreateInput>
  /**
   * In case the ReviewResponse was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.ReviewResponseUpdateInput, Prisma.ReviewResponseUncheckedUpdateInput>
}

/**
 * ReviewResponse delete
 */
export type ReviewResponseDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReviewResponse
   */
  select?: Prisma.ReviewResponseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReviewResponse
   */
  omit?: Prisma.ReviewResponseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReviewResponseInclude<ExtArgs> | null
  /**
   * Filter which ReviewResponse to delete.
   */
  where: Prisma.ReviewResponseWhereUniqueInput
}

/**
 * ReviewResponse deleteMany
 */
export type ReviewResponseDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which ReviewResponses to delete
   */
  where?: Prisma.ReviewResponseWhereInput
  /**
   * Limit how many ReviewResponses to delete.
   */
  limit?: number
}

/**
 * ReviewResponse without action
 */
export type ReviewResponseDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReviewResponse
   */
  select?: Prisma.ReviewResponseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReviewResponse
   */
  omit?: Prisma.ReviewResponseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReviewResponseInclude<ExtArgs> | null
}
