
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file should be your main import to use Prisma. Through it you get access to all the models, enums, and input types.
 *
 * 🟢 You can import this file directly.
 */

import * as process from 'node:process'
import * as path from 'node:path'
import { fileURLToPath } from 'node:url'
const __dirname = path.dirname(fileURLToPath(import.meta.url))

import * as runtime from "@prisma/client/runtime/library"
import * as $Enums from "./enums.js"
import * as $Class from "./internal/class.js"
import * as Prisma from "./internal/prismaNamespace.js"

export * as $Enums from './enums.js'
/**
 * ## Prisma Client
 * 
 * Type-safe database client for TypeScript
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Users
 * const users = await prisma.user.findMany()
 * ```
 * 
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export const PrismaClient = $Class.getPrismaClientClass(__dirname)
export type PrismaClient<ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions, Log = $Class.LogOptions<ClientOptions>, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = $Class.PrismaClient<ClientOptions, Log, ExtArgs>
export { Prisma }


// file annotations for bundling tools to include these files
path.join(__dirname, "libquery_engine-darwin-arm64.dylib.node")
path.join(process.cwd(), "prisma/generated/libquery_engine-darwin-arm64.dylib.node")

/**
 * Model User
 * 
 */
export type User = Prisma.UserModel
/**
 * Model Project
 * 
 */
export type Project = Prisma.ProjectModel
/**
 * Model Review
 * 
 */
export type Review = Prisma.ReviewModel
/**
 * Model ReviewResponse
 * 
 */
export type ReviewResponse = Prisma.ReviewResponseModel
/**
 * Model AIAnalysis
 * 
 */
export type AIAnalysis = Prisma.AIAnalysisModel

export type ReviewDimension = $Enums.ReviewDimension
export const ReviewDimension = $Enums.ReviewDimension
