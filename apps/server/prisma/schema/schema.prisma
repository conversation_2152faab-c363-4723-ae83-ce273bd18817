generator client {
  provider = "prisma-client"
  output   = "../generated"
  moduleFormat = "esm"
}

datasource db {
  provider = "postgres"
  url      = env("DATABASE_URL")
}

model User {
  id            String   @id @default(cuid())
  email         String   @unique
  name          String
  isKYCVerified Boolean  @default(false)
  createdAt     DateTime @default(now())
  
  reviews Review[]
  
  @@map("users")
}

model Project {
  id                   String   @id @default(cuid())
  title                String
  description          String
  imageUrl             String?
  websiteUrl           String?
  deckUrl              String?
  whitepaperUrl        String?
  socialUrls           Json?
  challengeIntro       String
  isApprovedForVoting  Boolean  @default(false)
  createdAt            DateTime @default(now())

  reviews Review[]
  aiAnalysis AIAnalysis?

  @@map("projects")
}

model Review {
  id                      String   @id @default(cuid())
  userId                  String
  projectId               String
  overallSentiment        Boolean
  overallComments         String?
  overallCommentsRelevant Boolean  @default(true)
  overallRelevanceScore   Int      @default(50)
  overallValidationReason String?
  createdAt               DateTime @default(now())
  
  user      User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  project   Project           @relation(fields: [projectId], references: [id], onDelete: Cascade)
  responses ReviewResponse[]
  
  @@unique([userId, projectId])
  @@map("reviews")
}

enum ReviewDimension {
  PROJECT_FUNDAMENTALS
  TEAM_GOVERNANCE
  TRANSPARENCY_DOCUMENTATION
  TECHNOLOGY_EXECUTION
  COMMUNITY_COMMUNICATION
  TOKEN_UTILITY_TOKENOMICS
}

model ReviewResponse {
  id               String          @id @default(cuid())
  reviewId         String
  dimension        ReviewDimension
  questionIndex    Int
  vote             Boolean
  feedback         String?
  feedbackRelevant Boolean         @default(true)
  relevanceScore   Int             @default(50)
  validationReason String?
  aiConfidence     Int             @default(0)

  review Review @relation(fields: [reviewId], references: [id], onDelete: Cascade)

  @@unique([reviewId, dimension, questionIndex])
  @@map("review_responses")
}

model AIAnalysis {
  id                          String   @id @default(cuid())
  projectId                   String   @unique
  projectFundamentalsScore    Int      @default(0)
  teamGovernanceScore         Int      @default(0)
  transparencyDocScore        Int      @default(0)
  technologyExecutionScore    Int      @default(0)
  communityCommunicationScore Int      @default(0)
  tokenUtilityTokenomicsScore Int      @default(0)
  overallScore                Int      @default(0)
  analysis                    String?
  reasoning                   String?
  strengths                   String[]
  weaknesses                  String[]
  recommendations             String[]
  confidence                  Int      @default(0)
  analysisVersion             String   @default("1.0")
  createdAt                   DateTime @default(now())
  updatedAt                   DateTime @updatedAt

  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@map("ai_analyses")
}
