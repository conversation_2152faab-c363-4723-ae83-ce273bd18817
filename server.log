$ turbo -F server dev
turbo 2.5.4

 WARNING  Unable to calculate transitive closures: No lockfile entry found for '@emnapi/core'
• Packages in scope: server
• Running dev in 1 packages
• Remote caching disabled
server:dev: cache bypass, force executing 840af92dd6a7728a
server:dev: $ next dev --turbopack
server:dev:    ▲ Next.js 15.3.0 (Turbopack)
server:dev:    - Local:        http://localhost:3000
server:dev:    - Network:      http://**************:3000
server:dev:    - Environments: .env
server:dev: 
server:dev:  ✓ Starting...
server:dev:  ✓ Compiled middleware in 197ms
server:dev:  ✓ Ready in 1141ms
server:dev:  ○ Compiling /trpc/[trpc] ...
server:dev:  ✓ Compiled /trpc/[trpc] in 1196ms
server:dev:  ✓ Compiled / in 306ms
server:dev:  GET / 200 in 360ms
server:dev:  GET /trpc/healthCheck?batch=1&input=%7B%7D 200 in 1893ms
server:dev:  GET / 200 in 27ms
server:dev: 
server:dev: -----
server:dev: [1m[31mFATAL[39m[0m: An unexpected Turbopack error occurred. A panic log has been written to /var/folders/70/_663vq515dx78fc48jy019m80000gn/T/next-panic-fe0f1d15f64b441ff0f3e9282bb7c841.log.
server:dev: 
server:dev: To help make Turbopack better, report this error by ]8;;https://github.com/vercel/next.js/discussions/new?category=turbopack-error-report&title=Turbopack%20Error%3A%20Next.js%20package%20not%20found&body=Turbopack%20version%3A%20%60v15.3.0-canary.45-23-g664110c18%60%0A%0AError%20message%3A%0A%60%60%60%0ANext.js%20package%20not%20found%0A%0ADebug%20info%3A%0A-%20Execution%20of%20get_entrypoints_with_issues_operation%20failed%0A-%20Execution%20of%20EntrypointsOperation%3A%3Anew%20failed%0A-%20Execution%20of%20Project%3A%3Aentrypoints%20failed%0A-%20Execution%20of%20AppProject%3A%3Aroutes%20failed%0A-%20Execution%20of%20directory_tree_to_entrypoints_internal%20failed%0A-%20Execution%20of%20get_next_package%20failed%0A-%20Next.js%20package%20not%20found%0A%60%60%60&labels=Turbopack,Turbopack%20Panic%20Backtrace\clicking here.]8;;\
server:dev: -----
server:dev: 
server:dev: [Error [TurbopackInternalError]: Next.js package not found
server:dev: 
server:dev: Debug info:
server:dev: - Execution of get_entrypoints_with_issues_operation failed
server:dev: - Execution of EntrypointsOperation::new failed
server:dev: - Execution of Project::entrypoints failed
server:dev: - Execution of AppProject::routes failed
server:dev: - Execution of directory_tree_to_entrypoints_internal failed
server:dev: - Execution of get_next_package failed
server:dev: - Next.js package not found]
server:dev: [?25h

 Tasks:    1 successful, 1 total
Cached:    0 cached, 1 total
  Time:    24m18.785s 

