# ATTN - Community Intelligence Engine

## **Community Review System**

### Access

- Only KYCed registered users can participate.
- Voting available when a project is submitted to the Deal Desk, Incubators, Accelerator, Launchpad, CEX, or Battle of the Chains.
- Voting request to the community needs to be approved by the applicant project.

### Voting Format

Each reviewer submits:

1. Upvote/Downvote for each review dimension (This will create a spider graph)
2. Short written feedback for each review dimension
3. Final comments or additional thoughts
4. Overall Sentiment (Upvote/Downvote) - Spider graph with 6-dimensions, each scored 0-100%.

## **Review Dimensions and questions**

### **1. Project Fundamentals**

*(Integrated: Narrative Strength + GTM Strategy)*

- How clearly is the product vision articulated?
- How original or differentiated is the idea compared to existing projects?
- Is there a real market demand or defined pain point the product addresses?
- Does the project present a compelling narrative or cultural hook?
- Can the concept be easily communicated in 1–2 sentences (“memeable”)?
- How likely is the idea to gain viral traction or benefit from social momentum?
- Does the project have a coherent go-to-market strategy?
- Are the growth channels (e.g., influencers, integrations, community-led growth) well-defined?

### **2. Team & Governance**

- Do the founders have relevant and verifiable experience?
- Is the identity of the core team publicly verifiable?
- Are all key functional areas (tech, product, marketing, legal, operations) adequately covered?
- Does the governance structure reflect transparency and accountability? *(if this is a feature)*
- Are advisors credible and actively involved?
- Is there a roadmap for progressive decentralization

### **3. Transparency & Documentation**

- Is the website professional, complete, and informative?
- Are whitepaper and/or Gitbook detailed and technically sound?
- Are risk disclosures, disclaimers, and legal structures clearly explained?
- Is there sufficient explanation of business model, and protocol operations?
- Does the roadmap provide a realistic timeline and measurable milestones?
- Are investor terms and compliance processes (KYC, SAFT, etc.) disclosed where relevant?

### **4. Technology & Execution**

- Is there an MVP, prototype, or demo available?
- Is the codebase public? Is there regular and recent GitHub activity?
- Are smart contracts audited or undergoing security reviews?
- Is the tech stack appropriate and scalable?
- Does the system architecture allow for modular growth or integrations?
- Are there dev team bios, repos, or activity logs to assess real progress?

### **5. Community & Communication**

- Is the project growing its community organically across key platforms (X, Discord, Telegram)?
- Is there consistent communication (AMAs, updates, roadmap progress)?
- Is the tone of engagement professional, transparent, and interactive?
- Are there clear community feedback loops or governance mechanisms (polls, discussions)?
- Is the branding cohesive and memorable?
- Is the project leveraging media or narratives to build trust and hype effectively?

### **6. Token Utility & Tokenomics**

- Is the token integral to the protocol (beyond speculation)?
- Is the allocation structure fair across stakeholders (founders, investors, community)?
- Are tokenomics aligned with long-term sustainability (burns, buybacks, inflation caps)?
- Are vesting schedules reasonable and aligned with value creation?
- Are incentives structured to retain users and contributors?
- Is the token designed to accrue value through utility, participation, or ecosystem growth?

### Votes Scoring

| **Dimension** | **# Questions** | **Scoring Method (Normalized to 100%)** |
| --- | --- | --- |
| **1. Project Fundamentals** | 6 | (👍 votes / 6) × 100% |
| **2. Team & Governance** | 6 | (👍 votes / 6) × 100% |
| **3. Transparency & Documentation** | 5 | (👍 votes / 5) × 100% |
| **4. Technology & Execution** | 5 | (👍 votes / 5) × 100% |
| **5. Community & Communication** | 6 | (👍 votes / 6) × 100% |
| **6. Token Utility & Tokenomics** | 5 | (👍 votes / 5) × 100% |
- Each question gets a simple thumbs up (👍 = 1) or thumbs down (👎 = 0)
- Min Score is 0 while max score per dimension is 100
- This sentiment score should not be confused with the reviewer score for reviewing projects on the CIE.