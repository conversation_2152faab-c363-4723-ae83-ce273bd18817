$ turbo dev
turbo 2.5.4

 WARNING  Unable to calculate transitive closures: No lockfile entry found for '@emnapi/core'
• Packages in scope: server, web
• Running dev in 2 packages
• Remote caching disabled
web:dev: cache bypass, force executing eb41573c8736b752
server:dev: cache bypass, force executing 840af92dd6a7728a
web:dev: $ react-router dev
server:dev: $ next dev --turbopack
server:dev:    ▲ Next.js 15.3.0 (Turbopack)
server:dev:    - Local:        http://localhost:3000
server:dev:    - Network:      http://**************:3000
server:dev:    - Environments: .env
server:dev: 
server:dev:  ✓ Starting...
server:dev:  ✓ Compiled middleware in 194ms
server:dev:  ✓ Ready in 1449ms
error: script "dev" was terminated by signal SIGTERM (Polite quit request)
web:dev: error: script "dev" exited with code 130
server:dev: [?25h
 ERROR  run failed: command  exited (1)
